{"build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\wheel-0.45.1-py310haa95532_0", "files": ["Lib/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "Lib/site-packages/wheel-0.45.1.dist-info/METADATA", "Lib/site-packages/wheel-0.45.1.dist-info/RECORD", "Lib/site-packages/wheel-0.45.1.dist-info/WHEEL", "Lib/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "Lib/site-packages/wheel/__init__.py", "Lib/site-packages/wheel/__main__.py", "Lib/site-packages/wheel/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/util.cpython-310.pyc", "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-310.pyc", "Lib/site-packages/wheel/_bdist_wheel.py", "Lib/site-packages/wheel/_setuptools_logging.py", "Lib/site-packages/wheel/bdist_wheel.py", "Lib/site-packages/wheel/cli/__init__.py", "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-310.pyc", "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-310.pyc", "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-310.pyc", "Lib/site-packages/wheel/cli/convert.py", "Lib/site-packages/wheel/cli/pack.py", "Lib/site-packages/wheel/cli/tags.py", "Lib/site-packages/wheel/cli/unpack.py", "Lib/site-packages/wheel/macosx_libfile.py", "Lib/site-packages/wheel/metadata.py", "Lib/site-packages/wheel/util.py", "Lib/site-packages/wheel/vendored/__init__.py", "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/LICENSE", "Lib/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "Lib/site-packages/wheel/vendored/packaging/LICENSE.BSD", "Lib/site-packages/wheel/vendored/packaging/__init__.py", "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/wheel/vendored/packaging/_parser.py", "Lib/site-packages/wheel/vendored/packaging/_structures.py", "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/wheel/vendored/packaging/markers.py", "Lib/site-packages/wheel/vendored/packaging/requirements.py", "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/wheel/vendored/packaging/tags.py", "Lib/site-packages/wheel/vendored/packaging/utils.py", "Lib/site-packages/wheel/vendored/packaging/version.py", "Lib/site-packages/wheel/vendored/vendor.txt", "Lib/site-packages/wheel/wheelfile.py", "Scripts/wheel-script.py", "Scripts/wheel.exe"], "fn": "wheel-0.45.1-py310haa95532_0.conda", "legacy_bz2_md5": "4afe1fc2823ea86f8f12bcc80bcee9cb", "legacy_bz2_size": 142232, "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\wheel-0.45.1-py310haa95532_0", "type": 1}, "md5": "2b5b7ec52f27ea8e0439546beb257713", "name": "wheel", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\wheel-0.45.1-py310haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "sha256_in_prefix": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "84ff7ac079957b1cbdec5eba7eec54f40a5baea8cf39194e833bcc1714a763f1", "sha256_in_prefix": "84ff7ac079957b1cbdec5eba7eec54f40a5baea8cf39194e833bcc1714a763f1", "size_in_bytes": 3188}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "Lib/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/wheel/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "eef43a97265c2b810916fcb5e0a85ecaaa444672197ad389034061b26169354e", "sha256_in_prefix": "eef43a97265c2b810916fcb5e0a85ecaaa444672197ad389034061b26169354e", "size_in_bytes": 201}, {"_path": "Lib/site-packages/wheel/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "621cc4c757e108cd19f74cbbe208173d6b89fc1469c65bd92aff3506a1131be4", "sha256_in_prefix": "621cc4c757e108cd19f74cbbe208173d6b89fc1469c65bd92aff3506a1131be4", "size_in_bytes": 599}, {"_path": "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "e966c0918df408c09fa0d68031f0889b17e2d1eeb39f40604837ea97fca27f67", "sha256_in_prefix": "e966c0918df408c09fa0d68031f0889b17e2d1eeb39f40604837ea97fca27f67", "size_in_bytes": 15181}, {"_path": "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "1711d4ee588a692634ed7d51f25eafab4671d3e66d43795130692c90d7f9c3e7", "sha256_in_prefix": "1711d4ee588a692634ed7d51f25eafab4671d3e66d43795130692c90d7f9c3e7", "size_in_bytes": 987}, {"_path": "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "a335a41415bb15f0db498fa19d9ad4e3733c602795e9d9ce32afdca0fe8900b1", "sha256_in_prefix": "a335a41415bb15f0db498fa19d9ad4e3733c602795e9d9ce32afdca0fe8900b1", "size_in_bytes": 661}, {"_path": "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "6111425e9d3062e0dacd4dd9fa747f68b40ef882a01e7abbab8d6722431ca0b6", "sha256_in_prefix": "6111425e9d3062e0dacd4dd9fa747f68b40ef882a01e7abbab8d6722431ca0b6", "size_in_bytes": 10417}, {"_path": "Lib/site-packages/wheel/__pycache__/metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "0488c9c2512c6ac2d9f4f798bc3a705ea9101de889ee862b06a32c4c0d366905", "sha256_in_prefix": "0488c9c2512c6ac2d9f4f798bc3a705ea9101de889ee862b06a32c4c0d366905", "size_in_bytes": 6158}, {"_path": "Lib/site-packages/wheel/__pycache__/util.cpython-310.pyc", "path_type": "hardlink", "sha256": "308ac8459558ce1725483f99eb80e859d1188a0694d474960d51502ec01381d4", "sha256_in_prefix": "308ac8459558ce1725483f99eb80e859d1188a0694d474960d51502ec01381d4", "size_in_bytes": 682}, {"_path": "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "3368e2f4be75b99292e4b749aeb3d5e854783a4e63f9fdd487acbb683ca593db", "sha256_in_prefix": "3368e2f4be75b99292e4b749aeb3d5e854783a4e63f9fdd487acbb683ca593db", "size_in_bytes": 6473}, {"_path": "Lib/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "Lib/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "0ab6043d98bbc346a1a05aa0fc90ba927c304b21b0c017dde25f36e42ec14c08", "sha256_in_prefix": "0ab6043d98bbc346a1a05aa0fc90ba927c304b21b0c017dde25f36e42ec14c08", "size_in_bytes": 4549}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-310.pyc", "path_type": "hardlink", "sha256": "9addab84f2393b1f3462676dfbea23057d8980505f2fedbac86939761094f86b", "sha256_in_prefix": "9addab84f2393b1f3462676dfbea23057d8980505f2fedbac86939761094f86b", "size_in_bytes": 9599}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-310.pyc", "path_type": "hardlink", "sha256": "f7ef1c161c072445fc28468b60f1d0502e92fa5a351c59b7014acac97b9693c8", "sha256_in_prefix": "f7ef1c161c072445fc28468b60f1d0502e92fa5a351c59b7014acac97b9693c8", "size_in_bytes": 3069}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "d31516afd71dae632cf3453f72379c7cb06d8612fedc862acc1c143cdd13142e", "sha256_in_prefix": "d31516afd71dae632cf3453f72379c7cb06d8612fedc862acc1c143cdd13142e", "size_in_bytes": 3818}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-310.pyc", "path_type": "hardlink", "sha256": "a4526622d36b3830d2eb38a1621b58d629e393b03e557c954dd640148e3c88fb", "sha256_in_prefix": "a4526622d36b3830d2eb38a1621b58d629e393b03e557c954dd640148e3c88fb", "size_in_bytes": 1071}, {"_path": "Lib/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "Lib/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b5a4bc97d0ff5cb04a9b2d9c2abb3921ee70fc80253022a791910715bf3027a3", "sha256_in_prefix": "b5a4bc97d0ff5cb04a9b2d9c2abb3921ee70fc80253022a791910715bf3027a3", "size_in_bytes": 137}, {"_path": "Lib/site-packages/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "247f8a9e9d80d57adca4be18ae92001fc8a0c711b32599e732787a9e46efe1e5", "sha256_in_prefix": "247f8a9e9d80d57adca4be18ae92001fc8a0c711b32599e732787a9e46efe1e5", "size_in_bytes": 147}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "hardlink", "sha256": "161f16538786011a2a3a6ab3fbd263b4c757b1c4f1b4c9c93eddef79c1be085f", "sha256_in_prefix": "161f16538786011a2a3a6ab3fbd263b4c757b1c4f1b4c9c93eddef79c1be085f", "size_in_bytes": 3269}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "3e1053aacf052fe70eec5b751512e7b9a4b12e0203587d087ddc024161f8c501", "sha256_in_prefix": "3e1053aacf052fe70eec5b751512e7b9a4b12e0203587d087ddc024161f8c501", "size_in_bytes": 6378}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "c501ffbbd467db025fd87d6efa276ff5006316b7c29ecab32f6cdeb955401fd6", "sha256_in_prefix": "c501ffbbd467db025fd87d6efa276ff5006316b7c29ecab32f6cdeb955401fd6", "size_in_bytes": 3297}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "445497ee8715c0b0cfa7312f40fe2ab644d7eee2e2de2f7822b9bad244792fff", "sha256_in_prefix": "445497ee8715c0b0cfa7312f40fe2ab644d7eee2e2de2f7822b9bad244792fff", "size_in_bytes": 8921}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "hardlink", "sha256": "f1a39dc93572714a9be17133351c1110b01afb1b5f3b84ee11ad45533f078167", "sha256_in_prefix": "f1a39dc93572714a9be17133351c1110b01afb1b5f3b84ee11ad45533f078167", "size_in_bytes": 2659}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "hardlink", "sha256": "202a0f302ccc2599e351e662ff64d6d43517c99eaee40579aaf0eb48777e9f14", "sha256_in_prefix": "202a0f302ccc2599e351e662ff64d6d43517c99eaee40579aaf0eb48777e9f14", "size_in_bytes": 5779}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "35b9398652fb8102d5a9625b356dbfe453ce58b3380068a7f3ca670a2dc5d6d3", "sha256_in_prefix": "35b9398652fb8102d5a9625b356dbfe453ce58b3380068a7f3ca670a2dc5d6d3", "size_in_bytes": 6865}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "hardlink", "sha256": "62818e6d1353f91355bfc8a9787b65e77c0adb4ccbd3b2a83c3eac7cd02a353e", "sha256_in_prefix": "62818e6d1353f91355bfc8a9787b65e77c0adb4ccbd3b2a83c3eac7cd02a353e", "size_in_bytes": 2792}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "hardlink", "sha256": "4f5a37e9f72a9a90c0bb2f832beba5aa41fe94901e780bc36e4d756396fafb11", "sha256_in_prefix": "4f5a37e9f72a9a90c0bb2f832beba5aa41fe94901e780bc36e4d756396fafb11", "size_in_bytes": 30951}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "40586b09640b60dd1a165e3a749821cd02f0f55127c7e51b1bd7051b4ae2951d", "sha256_in_prefix": "40586b09640b60dd1a165e3a749821cd02f0f55127c7e51b1bd7051b4ae2951d", "size_in_bytes": 13759}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "cca434172207475a1189ec26ea107685566038388962f37b666816dcab5c63a2", "sha256_in_prefix": "cca434172207475a1189ec26ea107685566038388962f37b666816dcab5c63a2", "size_in_bytes": 4477}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "efe0abee6fa6f91ed3b31681db35985daca07fb5351e6d55a6716829f5408548", "sha256_in_prefix": "efe0abee6fa6f91ed3b31681db35985daca07fb5351e6d55a6716829f5408548", "size_in_bytes": 14122}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Scripts/wheel-script.py", "path_type": "hardlink", "sha256": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "sha256_in_prefix": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "size_in_bytes": 203}, {"_path": "Scripts/wheel.exe", "path_type": "hardlink", "sha256": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "sha256_in_prefix": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "size_in_bytes": 54032}], "paths_version": 1}, "requested_spec": "None", "sha256": "957d3e22ded3a6c1ea36782a7bc481907e5318e4c59d7aa1d3685c807fc934c2", "size": 148932, "subdir": "win-64", "timestamp": 1737990665883, "url": "https://repo.anaconda.com/pkgs/main/win-64/wheel-0.45.1-py310haa95532_0.conda", "version": "0.45.1"}