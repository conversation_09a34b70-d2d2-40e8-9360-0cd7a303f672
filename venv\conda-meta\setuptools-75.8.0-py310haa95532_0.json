{"build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-75.8.0-py310haa95532_0", "files": ["Lib/site-packages/_distutils_hack/__init__.py", "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-310.pyc", "Lib/site-packages/_distutils_hack/override.py", "Lib/site-packages/distutils-precedence.pth", "Lib/site-packages/pkg_resources/__init__.py", "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pkg_resources/api_tests.txt", "Lib/site-packages/pkg_resources/py.typed", "Lib/site-packages/pkg_resources/tests/__init__.py", "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "Lib/site-packages/pkg_resources/tests/test_markers.py", "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "Lib/site-packages/pkg_resources/tests/test_resources.py", "Lib/site-packages/pkg_resources/tests/test_working_set.py", "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/PKG-INFO", "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/SOURCES.txt", "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/dependency_links.txt", "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/entry_points.txt", "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/requires.txt", "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/top_level.txt", "Lib/site-packages/setuptools/__init__.py", "Lib/site-packages/setuptools/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_imp.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_path.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_static.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/depends.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/discovery.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/dist.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/extension.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/glob.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/installer.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/launch.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/logging.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/modified.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/monkey.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/msvc.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/package_index.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/warnings.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-310.pyc", "Lib/site-packages/setuptools/_core_metadata.py", "Lib/site-packages/setuptools/_distutils/__init__.py", "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/_log.py", "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "Lib/site-packages/setuptools/_distutils/_modified.py", "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/archive_util.py", "Lib/site-packages/setuptools/_distutils/ccompiler.py", "Lib/site-packages/setuptools/_distutils/cmd.py", "Lib/site-packages/setuptools/_distutils/command/__init__.py", "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "Lib/site-packages/setuptools/_distutils/command/bdist.py", "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/command/build.py", "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "Lib/site-packages/setuptools/_distutils/command/build_py.py", "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "Lib/site-packages/setuptools/_distutils/command/check.py", "Lib/site-packages/setuptools/_distutils/command/clean.py", "Lib/site-packages/setuptools/_distutils/command/config.py", "Lib/site-packages/setuptools/_distutils/command/install.py", "Lib/site-packages/setuptools/_distutils/command/install_data.py", "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "Lib/site-packages/setuptools/_distutils/command/sdist.py", "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compat/py39.py", "Lib/site-packages/setuptools/_distutils/core.py", "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/debug.py", "Lib/site-packages/setuptools/_distutils/dep_util.py", "Lib/site-packages/setuptools/_distutils/dir_util.py", "Lib/site-packages/setuptools/_distutils/dist.py", "Lib/site-packages/setuptools/_distutils/errors.py", "Lib/site-packages/setuptools/_distutils/extension.py", "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "Lib/site-packages/setuptools/_distutils/file_util.py", "Lib/site-packages/setuptools/_distutils/filelist.py", "Lib/site-packages/setuptools/_distutils/log.py", "Lib/site-packages/setuptools/_distutils/spawn.py", "Lib/site-packages/setuptools/_distutils/sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_ccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cygwinccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_mingwccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_msvccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_unixccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "Lib/site-packages/setuptools/_distutils/tests/support.py", "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_ccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "Lib/site-packages/setuptools/_distutils/tests/test_cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "Lib/site-packages/setuptools/_distutils/tests/test_mingwccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "Lib/site-packages/setuptools/_distutils/tests/test_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "Lib/site-packages/setuptools/_distutils/tests/test_unixccompiler.py", "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "Lib/site-packages/setuptools/_distutils/text_file.py", "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "Lib/site-packages/setuptools/_distutils/util.py", "Lib/site-packages/setuptools/_distutils/version.py", "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "Lib/site-packages/setuptools/_entry_points.py", "Lib/site-packages/setuptools/_imp.py", "Lib/site-packages/setuptools/_importlib.py", "Lib/site-packages/setuptools/_itertools.py", "Lib/site-packages/setuptools/_normalization.py", "Lib/site-packages/setuptools/_path.py", "Lib/site-packages/setuptools/_reqs.py", "Lib/site-packages/setuptools/_shutil.py", "Lib/site-packages/setuptools/_static.py", "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/packaging/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "Lib/site-packages/setuptools/_vendor/wheel/util.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "Lib/site-packages/setuptools/archive_util.py", "Lib/site-packages/setuptools/build_meta.py", "Lib/site-packages/setuptools/cli-32.exe", "Lib/site-packages/setuptools/cli-64.exe", "Lib/site-packages/setuptools/cli-arm64.exe", "Lib/site-packages/setuptools/cli.exe", "Lib/site-packages/setuptools/command/__init__.py", "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/test.cpython-310.pyc", "Lib/site-packages/setuptools/command/_requirestxt.py", "Lib/site-packages/setuptools/command/alias.py", "Lib/site-packages/setuptools/command/bdist_egg.py", "Lib/site-packages/setuptools/command/bdist_rpm.py", "Lib/site-packages/setuptools/command/bdist_wheel.py", "Lib/site-packages/setuptools/command/build.py", "Lib/site-packages/setuptools/command/build_clib.py", "Lib/site-packages/setuptools/command/build_ext.py", "Lib/site-packages/setuptools/command/build_py.py", "Lib/site-packages/setuptools/command/develop.py", "Lib/site-packages/setuptools/command/dist_info.py", "Lib/site-packages/setuptools/command/easy_install.py", "Lib/site-packages/setuptools/command/editable_wheel.py", "Lib/site-packages/setuptools/command/egg_info.py", "Lib/site-packages/setuptools/command/install.py", "Lib/site-packages/setuptools/command/install_egg_info.py", "Lib/site-packages/setuptools/command/install_lib.py", "Lib/site-packages/setuptools/command/install_scripts.py", "Lib/site-packages/setuptools/command/launcher manifest.xml", "Lib/site-packages/setuptools/command/rotate.py", "Lib/site-packages/setuptools/command/saveopts.py", "Lib/site-packages/setuptools/command/sdist.py", "Lib/site-packages/setuptools/command/setopt.py", "Lib/site-packages/setuptools/command/test.py", "Lib/site-packages/setuptools/compat/__init__.py", "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/compat/py310.py", "Lib/site-packages/setuptools/compat/py311.py", "Lib/site-packages/setuptools/compat/py312.py", "Lib/site-packages/setuptools/compat/py39.py", "Lib/site-packages/setuptools/config/NOTICE", "Lib/site-packages/setuptools/config/__init__.py", "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-310.pyc", "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "Lib/site-packages/setuptools/config/distutils.schema.json", "Lib/site-packages/setuptools/config/expand.py", "Lib/site-packages/setuptools/config/pyprojecttoml.py", "Lib/site-packages/setuptools/config/setupcfg.py", "Lib/site-packages/setuptools/config/setuptools.schema.json", "Lib/site-packages/setuptools/depends.py", "Lib/site-packages/setuptools/discovery.py", "Lib/site-packages/setuptools/dist.py", "Lib/site-packages/setuptools/errors.py", "Lib/site-packages/setuptools/extension.py", "Lib/site-packages/setuptools/glob.py", "Lib/site-packages/setuptools/gui-32.exe", "Lib/site-packages/setuptools/gui-64.exe", "Lib/site-packages/setuptools/gui-arm64.exe", "Lib/site-packages/setuptools/gui.exe", "Lib/site-packages/setuptools/installer.py", "Lib/site-packages/setuptools/launch.py", "Lib/site-packages/setuptools/logging.py", "Lib/site-packages/setuptools/modified.py", "Lib/site-packages/setuptools/monkey.py", "Lib/site-packages/setuptools/msvc.py", "Lib/site-packages/setuptools/namespaces.py", "Lib/site-packages/setuptools/package_index.py", "Lib/site-packages/setuptools/sandbox.py", "Lib/site-packages/setuptools/script (dev).tmpl", "Lib/site-packages/setuptools/script.tmpl", "Lib/site-packages/setuptools/tests/__init__.py", "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-310.pyc", "Lib/site-packages/setuptools/tests/compat/__init__.py", "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/tests/compat/py39.py", "Lib/site-packages/setuptools/tests/config/__init__.py", "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_expand.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "Lib/site-packages/setuptools/tests/contexts.py", "Lib/site-packages/setuptools/tests/environment.py", "Lib/site-packages/setuptools/tests/fixtures.py", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "Lib/site-packages/setuptools/tests/integration/__init__.py", "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-310.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-310.pyc", "Lib/site-packages/setuptools/tests/integration/helpers.py", "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "Lib/site-packages/setuptools/tests/mod_with_constant.py", "Lib/site-packages/setuptools/tests/namespaces.py", "Lib/site-packages/setuptools/tests/script-with-bom.py", "Lib/site-packages/setuptools/tests/server.py", "Lib/site-packages/setuptools/tests/test_archive_util.py", "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "Lib/site-packages/setuptools/tests/test_build.py", "Lib/site-packages/setuptools/tests/test_build_clib.py", "Lib/site-packages/setuptools/tests/test_build_ext.py", "Lib/site-packages/setuptools/tests/test_build_meta.py", "Lib/site-packages/setuptools/tests/test_build_py.py", "Lib/site-packages/setuptools/tests/test_config_discovery.py", "Lib/site-packages/setuptools/tests/test_core_metadata.py", "Lib/site-packages/setuptools/tests/test_depends.py", "Lib/site-packages/setuptools/tests/test_develop.py", "Lib/site-packages/setuptools/tests/test_dist.py", "Lib/site-packages/setuptools/tests/test_dist_info.py", "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "Lib/site-packages/setuptools/tests/test_easy_install.py", "Lib/site-packages/setuptools/tests/test_editable_install.py", "Lib/site-packages/setuptools/tests/test_egg_info.py", "Lib/site-packages/setuptools/tests/test_extern.py", "Lib/site-packages/setuptools/tests/test_find_packages.py", "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "Lib/site-packages/setuptools/tests/test_glob.py", "Lib/site-packages/setuptools/tests/test_install_scripts.py", "Lib/site-packages/setuptools/tests/test_logging.py", "Lib/site-packages/setuptools/tests/test_manifest.py", "Lib/site-packages/setuptools/tests/test_namespaces.py", "Lib/site-packages/setuptools/tests/test_packageindex.py", "Lib/site-packages/setuptools/tests/test_sandbox.py", "Lib/site-packages/setuptools/tests/test_sdist.py", "Lib/site-packages/setuptools/tests/test_setopt.py", "Lib/site-packages/setuptools/tests/test_setuptools.py", "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "Lib/site-packages/setuptools/tests/test_virtualenv.py", "Lib/site-packages/setuptools/tests/test_warnings.py", "Lib/site-packages/setuptools/tests/test_wheel.py", "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "Lib/site-packages/setuptools/tests/text.py", "Lib/site-packages/setuptools/tests/textwrap.py", "Lib/site-packages/setuptools/unicode_utils.py", "Lib/site-packages/setuptools/version.py", "Lib/site-packages/setuptools/warnings.py", "Lib/site-packages/setuptools/wheel.py", "Lib/site-packages/setuptools/windows_support.py"], "fn": "setuptools-75.8.0-py310haa95532_0.conda", "legacy_bz2_md5": "b224737684394d930e0e50b765b0d23e", "legacy_bz2_size": 1772555, "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-75.8.0-py310haa95532_0", "type": 1}, "md5": "c0f54857180019876ec431e9de780214", "name": "setuptools", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-75.8.0-py310haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "2a468be7e4960977cc6c1478d2c8c95d3e10135b48b6e92318a851579e459217", "sha256_in_prefix": "2a468be7e4960977cc6c1478d2c8c95d3e10135b48b6e92318a851579e459217", "size_in_bytes": 8195}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-310.pyc", "path_type": "hardlink", "sha256": "c24ba6028f1cda817bd461d26ea6f1b2e6c780ce6901f9e20a5acade03e7da04", "sha256_in_prefix": "c24ba6028f1cda817bd461d26ea6f1b2e6c780ce6901f9e20a5acade03e7da04", "size_in_bytes": 189}, {"_path": "Lib/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "Lib/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "sha256_in_prefix": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "7eec9d77ef3ba27df5fb73f4c13353662167d2aba2635f2965d3facd15da950b", "sha256_in_prefix": "7eec9d77ef3ba27df5fb73f4c13353662167d2aba2635f2965d3facd15da950b", "size_in_bytes": 126098}, {"_path": "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a3df2fd9164c24567260e78e8dfcf8632549725cdb09d5091aeb8a299fc17b98", "sha256_in_prefix": "a3df2fd9164c24567260e78e8dfcf8632549725cdb09d5091aeb8a299fc17b98", "size_in_bytes": 115588}, {"_path": "Lib/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "Lib/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "9e7e0419136a01ee6020bac64c306d2f99854d5f4d8003b89209ab983376d062", "sha256_in_prefix": "9e7e0419136a01ee6020bac64c306d2f99854d5f4d8003b89209ab983376d062", "size_in_bytes": 142}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-310.pyc", "path_type": "hardlink", "sha256": "baf064cab8b86b9c2aeea9e11e16e33e52494cf28cde668be280da2de73affec", "sha256_in_prefix": "baf064cab8b86b9c2aeea9e11e16e33e52494cf28cde668be280da2de73affec", "size_in_bytes": 2343}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a56b1ce9fbdbdc92da735f07d249b0306e9daa8282af539f57955c66160d492", "sha256_in_prefix": "0a56b1ce9fbdbdc92da735f07d249b0306e9daa8282af539f57955c66160d492", "size_in_bytes": 1544}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "9b1e94ae746d480967ba59d60032e231af5043ee81370c00936b3ab486e187a8", "sha256_in_prefix": "9b1e94ae746d480967ba59d60032e231af5043ee81370c00936b3ab486e187a8", "size_in_bytes": 482}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-310.pyc", "path_type": "hardlink", "sha256": "d5506560ece31ec2c872e0ef339908fe2a17f8c79572139e1d12fb48f447645e", "sha256_in_prefix": "d5506560ece31ec2c872e0ef339908fe2a17f8c79572139e1d12fb48f447645e", "size_in_bytes": 13557}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-310.pyc", "path_type": "hardlink", "sha256": "eb4b7a4c6f38d6e651e1fef71f0aefa755146abc63967dfc4d49d3b627ced172", "sha256_in_prefix": "eb4b7a4c6f38d6e651e1fef71f0aefa755146abc63967dfc4d49d3b627ced172", "size_in_bytes": 28141}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-310.pyc", "path_type": "hardlink", "sha256": "ae9c81d3fe0e6278d30f951527af692c4db6d85cee12fe663cec8b1029608d1d", "sha256_in_prefix": "ae9c81d3fe0e6278d30f951527af692c4db6d85cee12fe663cec8b1029608d1d", "size_in_bytes": 8399}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-310.pyc", "path_type": "hardlink", "sha256": "d9942c5d42929dcdc960c546bbf506b43663055777d0f7649156749433288915", "sha256_in_prefix": "d9942c5d42929dcdc960c546bbf506b43663055777d0f7649156749433288915", "size_in_bytes": 272}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "Lib/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "50c8054c117c58dfb204e69672c98a96bd70a84cfb72ed81f4e9cf79732690c2", "sha256_in_prefix": "50c8054c117c58dfb204e69672c98a96bd70a84cfb72ed81f4e9cf79732690c2", "size_in_bytes": 15207}, {"_path": "Lib/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "Lib/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "65427c4aee3bbf561f44391ff01ea704f1dc742855773811731a691d3c09b3d9", "sha256_in_prefix": "65427c4aee3bbf561f44391ff01ea704f1dc742855773811731a691d3c09b3d9", "size_in_bytes": 8539}, {"_path": "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "134bcab7a060628587e9253886abd43ef6319a069a5e8ca62e998bb8463425cd", "sha256_in_prefix": "134bcab7a060628587e9253886abd43ef6319a069a5e8ca62e998bb8463425cd", "size_in_bytes": 6812}, {"_path": "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "970b9f3b051f0faea3698b003c438cc13e98e16c4969da6f65653d2502763dc4", "sha256_in_prefix": "970b9f3b051f0faea3698b003c438cc13e98e16c4969da6f65653d2502763dc4", "size_in_bytes": 23773}, {"_path": "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/requires.txt", "path_type": "hardlink", "sha256": "16b1af3f936778ac0ed4d4a41aad8eba95c4fcadaa7085e9542ea6b10d1d3e7c", "sha256_in_prefix": "16b1af3f936778ac0ed4d4a41aad8eba95c4fcadaa7085e9542ea6b10d1d3e7c", "size_in_bytes": 1260}, {"_path": "Lib/site-packages/setuptools-75.8.0-py3.10.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "Lib/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "sha256_in_prefix": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "size_in_bytes": 10406}, {"_path": "Lib/site-packages/setuptools/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "0b5d7baed6173b9174b2f65b5d1cb2d85d74f0bf11eece840da841bf848c88a8", "sha256_in_prefix": "0b5d7baed6173b9174b2f65b5d1cb2d85d74f0bf11eece840da841bf848c88a8", "size_in_bytes": 11047}, {"_path": "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "1c269fe798fb8da70aea9e738191918885dc27f4d632eacc92e542d030456baa", "sha256_in_prefix": "1c269fe798fb8da70aea9e738191918885dc27f4d632eacc92e542d030456baa", "size_in_bytes": 8984}, {"_path": "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-310.pyc", "path_type": "hardlink", "sha256": "8382ea1694cac57060aa913d26ab8cdeed78bf756b8785d60bd8ce3d2b5635ef", "sha256_in_prefix": "8382ea1694cac57060aa913d26ab8cdeed78bf756b8785d60bd8ce3d2b5635ef", "size_in_bytes": 3218}, {"_path": "Lib/site-packages/setuptools/__pycache__/_imp.cpython-310.pyc", "path_type": "hardlink", "sha256": "57eca201942a6f184744600599c653a64c215d55cf46a997d6406006fc44a09d", "sha256_in_prefix": "57eca201942a6f184744600599c653a64c215d55cf46a997d6406006fc44a09d", "size_in_bytes": 2054}, {"_path": "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-310.pyc", "path_type": "hardlink", "sha256": "23dba7bf69556f83253b2a2093ee683bccd0bd018cf5144ab602e137ee70a87a", "sha256_in_prefix": "23dba7bf69556f83253b2a2093ee683bccd0bd018cf5144ab602e137ee70a87a", "size_in_bytes": 310}, {"_path": "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-310.pyc", "path_type": "hardlink", "sha256": "0b9450bdd1c95883313cd2bfbd243d2f42d159f4b4f277e6e0b4bc28b6194f01", "sha256_in_prefix": "0b9450bdd1c95883313cd2bfbd243d2f42d159f4b4f277e6e0b4bc28b6194f01", "size_in_bytes": 851}, {"_path": "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-310.pyc", "path_type": "hardlink", "sha256": "88d16582cb1aeff491f873098a7883cb95640e202517d9e708662567a9732edf", "sha256_in_prefix": "88d16582cb1aeff491f873098a7883cb95640e202517d9e708662567a9732edf", "size_in_bytes": 4610}, {"_path": "Lib/site-packages/setuptools/__pycache__/_path.cpython-310.pyc", "path_type": "hardlink", "sha256": "a5f97e3eed6c2250c8330409c9573aa525bf5f0c18134ed8916b98f744d761c8", "sha256_in_prefix": "a5f97e3eed6c2250c8330409c9573aa525bf5f0c18134ed8916b98f744d761c8", "size_in_bytes": 2874}, {"_path": "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-310.pyc", "path_type": "hardlink", "sha256": "dd9bb4907893afaa5e08235f5fa2306f1f9e5bdca73509c238a7ad6e60fe8fa6", "sha256_in_prefix": "dd9bb4907893afaa5e08235f5fa2306f1f9e5bdca73509c238a7ad6e60fe8fa6", "size_in_bytes": 1610}, {"_path": "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-310.pyc", "path_type": "hardlink", "sha256": "d72eb79ce77bfbd0454b1f58f10b3cfba2ecf3cb3be1d60090d0713371fb490d", "sha256_in_prefix": "d72eb79ce77bfbd0454b1f58f10b3cfba2ecf3cb3be1d60090d0713371fb490d", "size_in_bytes": 1700}, {"_path": "Lib/site-packages/setuptools/__pycache__/_static.cpython-310.pyc", "path_type": "hardlink", "sha256": "954c56fbc8f97014ac1cc87c11bc9a15538f3f1f952cf4b4bbeebe2eccf7d795", "sha256_in_prefix": "954c56fbc8f97014ac1cc87c11bc9a15538f3f1f952cf4b4bbeebe2eccf7d795", "size_in_bytes": 5141}, {"_path": "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "66aa6a9d8b128b3717d4330521030b0aaf8e9c650afea479ba3c94021264bcf4", "sha256_in_prefix": "66aa6a9d8b128b3717d4330521030b0aaf8e9c650afea479ba3c94021264bcf4", "size_in_bytes": 6170}, {"_path": "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "c4f935cabbeff411eb17fa165aa79cee8dab03b896787be899e76a9707239f04", "sha256_in_prefix": "c4f935cabbeff411eb17fa165aa79cee8dab03b896787be899e76a9707239f04", "size_in_bytes": 18344}, {"_path": "Lib/site-packages/setuptools/__pycache__/depends.cpython-310.pyc", "path_type": "hardlink", "sha256": "6b6f05b87cd3b11b1e42cf9b798f3afa2a0f7e829d5141a8a9388358e883e95e", "sha256_in_prefix": "6b6f05b87cd3b11b1e42cf9b798f3afa2a0f7e829d5141a8a9388358e883e95e", "size_in_bytes": 5430}, {"_path": "Lib/site-packages/setuptools/__pycache__/discovery.cpython-310.pyc", "path_type": "hardlink", "sha256": "e7b570ad734da5b65aeef7cee3afff76dc7096eb78d5f5e51202eaf1d9c3a3d9", "sha256_in_prefix": "e7b570ad734da5b65aeef7cee3afff76dc7096eb78d5f5e51202eaf1d9c3a3d9", "size_in_bytes": 21156}, {"_path": "Lib/site-packages/setuptools/__pycache__/dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "dbae60c9b9982b6f8490356888c0da56b6b0630062b63f692a865f9745d3e2ca", "sha256_in_prefix": "dbae60c9b9982b6f8490356888c0da56b6b0630062b63f692a865f9745d3e2ca", "size_in_bytes": 32560}, {"_path": "Lib/site-packages/setuptools/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "5a1126b6e1c254f674af6d3b9bd0c34df55400227e127ff8ad626964a83b355e", "sha256_in_prefix": "5a1126b6e1c254f674af6d3b9bd0c34df55400227e127ff8ad626964a83b355e", "size_in_bytes": 2830}, {"_path": "Lib/site-packages/setuptools/__pycache__/extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "530c8181c65b76919bbd789a40682196afe23a1428ed6e5d31a90781b9492dfc", "sha256_in_prefix": "530c8181c65b76919bbd789a40682196afe23a1428ed6e5d31a90781b9492dfc", "size_in_bytes": 6268}, {"_path": "Lib/site-packages/setuptools/__pycache__/glob.cpython-310.pyc", "path_type": "hardlink", "sha256": "8ee9a93d66fdfed3d9b01dbf6e9613cbf5508bdb4b63aa3ecb858fe260435216", "sha256_in_prefix": "8ee9a93d66fdfed3d9b01dbf6e9613cbf5508bdb4b63aa3ecb858fe260435216", "size_in_bytes": 5092}, {"_path": "Lib/site-packages/setuptools/__pycache__/installer.cpython-310.pyc", "path_type": "hardlink", "sha256": "ae769739e19415ffc213cf38d17e5fd8c5a40def40cba8e39c7f3e701460b9a6", "sha256_in_prefix": "ae769739e19415ffc213cf38d17e5fd8c5a40def40cba8e39c7f3e701460b9a6", "size_in_bytes": 4128}, {"_path": "Lib/site-packages/setuptools/__pycache__/launch.cpython-310.pyc", "path_type": "hardlink", "sha256": "b357898e59ad8bafd46f6da52dab78a5a1760f4b587754791df1eefbc56bb78e", "sha256_in_prefix": "b357898e59ad8bafd46f6da52dab78a5a1760f4b587754791df1eefbc56bb78e", "size_in_bytes": 880}, {"_path": "Lib/site-packages/setuptools/__pycache__/logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "1c39c817f2ed5e4404f92c92de1e0ff705a230829da09e98692cb1f4773331da", "sha256_in_prefix": "1c39c817f2ed5e4404f92c92de1e0ff705a230829da09e98692cb1f4773331da", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/__pycache__/modified.cpython-310.pyc", "path_type": "hardlink", "sha256": "d6decc684e376b2291e42dafe3b7d7bfc8f617d7d70a131e860bb36a92bce367", "sha256_in_prefix": "d6decc684e376b2291e42dafe3b7d7bfc8f617d7d70a131e860bb36a92bce367", "size_in_bytes": 397}, {"_path": "Lib/site-packages/setuptools/__pycache__/monkey.cpython-310.pyc", "path_type": "hardlink", "sha256": "3ff3e7a489de5fb1f5bd08d49bf2277af8a2d74a2eeb3dbbaac3249317720f34", "sha256_in_prefix": "3ff3e7a489de5fb1f5bd08d49bf2277af8a2d74a2eeb3dbbaac3249317720f34", "size_in_bytes": 3613}, {"_path": "Lib/site-packages/setuptools/__pycache__/msvc.cpython-310.pyc", "path_type": "hardlink", "sha256": "54fb52e3384ee4717258f86f84a18acb9be07fe7adaa8b50e7b822b0430dc9e6", "sha256_in_prefix": "54fb52e3384ee4717258f86f84a18acb9be07fe7adaa8b50e7b822b0430dc9e6", "size_in_bytes": 36068}, {"_path": "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-310.pyc", "path_type": "hardlink", "sha256": "ec063636a3bac56dfa95eb274370062b15bdb223ed6fa192354a936ef88ae958", "sha256_in_prefix": "ec063636a3bac56dfa95eb274370062b15bdb223ed6fa192354a936ef88ae958", "size_in_bytes": 3713}, {"_path": "Lib/site-packages/setuptools/__pycache__/package_index.cpython-310.pyc", "path_type": "hardlink", "sha256": "5507534cd7ef4ae9602b3591f9da146b6c692b3e63eeca468f578fd47b850bba", "sha256_in_prefix": "5507534cd7ef4ae9602b3591f9da146b6c692b3e63eeca468f578fd47b850bba", "size_in_bytes": 33570}, {"_path": "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-310.pyc", "path_type": "hardlink", "sha256": "9963f631c687a129e2b7505b7c22ce4f8bd1650b30a58a2ca2a3cb93f05fc87e", "sha256_in_prefix": "9963f631c687a129e2b7505b7c22ce4f8bd1650b30a58a2ca2a3cb93f05fc87e", "size_in_bytes": 16335}, {"_path": "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "94259862618ef320d3448ce4c94129135a5a262968064dd44af93e0568e305d8", "sha256_in_prefix": "94259862618ef320d3448ce4c94129135a5a262968064dd44af93e0568e305d8", "size_in_bytes": 3137}, {"_path": "Lib/site-packages/setuptools/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "9cff3a9e1433e7dc976b798db84b5522bd5de2607b8126dbb1db9f9a631c3035", "sha256_in_prefix": "9cff3a9e1433e7dc976b798db84b5522bd5de2607b8126dbb1db9f9a631c3035", "size_in_bytes": 288}, {"_path": "Lib/site-packages/setuptools/__pycache__/warnings.cpython-310.pyc", "path_type": "hardlink", "sha256": "d26413a49c52eeffe95be0c63cd4b80322c55a4a73c0d3cbb6c5ddb5a38c42b8", "sha256_in_prefix": "d26413a49c52eeffe95be0c63cd4b80322c55a4a73c0d3cbb6c5ddb5a38c42b8", "size_in_bytes": 3952}, {"_path": "Lib/site-packages/setuptools/__pycache__/wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "314f799d15dff99c9fbd8624fa0d5d353460554e425a2927e2b0f1f014eb0403", "sha256_in_prefix": "314f799d15dff99c9fbd8624fa0d5d353460554e425a2927e2b0f1f014eb0403", "size_in_bytes": 7754}, {"_path": "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-310.pyc", "path_type": "hardlink", "sha256": "5b5fb4109372b34e84226ce0b72366f72f73b2202a98d605a476301189c7b0ca", "sha256_in_prefix": "5b5fb4109372b34e84226ce0b72366f72f73b2202a98d605a476301189c7b0ca", "size_in_bytes": 1002}, {"_path": "Lib/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "dfc90134dc417b5490010f96b671fd732a3b47f60d378ec0750a9dd0e4f422ea", "sha256_in_prefix": "dfc90134dc417b5490010f96b671fd732a3b47f60d378ec0750a9dd0e4f422ea", "size_in_bytes": 11070}, {"_path": "Lib/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f2dba5bcf5a13a8f02161c6777514dfc13f1ffa21c8e459abb468ebf5fdaf1cf", "sha256_in_prefix": "f2dba5bcf5a13a8f02161c6777514dfc13f1ffa21c8e459abb468ebf5fdaf1cf", "size_in_bytes": 334}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-310.pyc", "path_type": "hardlink", "sha256": "bad229e04f855858f72cce30f5970f8bf77f96d3bf3723cfe466765ea36c18df", "sha256_in_prefix": "bad229e04f855858f72cce30f5970f8bf77f96d3bf3723cfe466765ea36c18df", "size_in_bytes": 185}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "724458ff864072985a682f250cc4237e19dc91e3f92a671fa09dbd544b4843b9", "sha256_in_prefix": "724458ff864072985a682f250cc4237e19dc91e3f92a671fa09dbd544b4843b9", "size_in_bytes": 408}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-310.pyc", "path_type": "hardlink", "sha256": "e9a438bffd79772c0d8eee69de24b23b835015373224dd0b339a95b9fa0fd089", "sha256_in_prefix": "e9a438bffd79772c0d8eee69de24b23b835015373224dd0b339a95b9fa0fd089", "size_in_bytes": 2939}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "5d016dad4b801564f2e2fa65f4ce1ef2041b2dfa24c0012acd16383da0e847d5", "sha256_in_prefix": "5d016dad4b801564f2e2fa65f4ce1ef2041b2dfa24c0012acd16383da0e847d5", "size_in_bytes": 14762}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "bbec90181e0e52d2f317ddb39198d363806a45c05d847cd7a3177fd5116ed98f", "sha256_in_prefix": "bbec90181e0e52d2f317ddb39198d363806a45c05d847cd7a3177fd5116ed98f", "size_in_bytes": 6099}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "092be824c561598896520cdcf381dff9e970059db36c7b16a832a373cd30d6bd", "sha256_in_prefix": "092be824c561598896520cdcf381dff9e970059db36c7b16a832a373cd30d6bd", "size_in_bytes": 35948}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-310.pyc", "path_type": "hardlink", "sha256": "c2955b7b7126bde70ea7f1749005e27a98c8f2a12368dcf5685ec2d8171c3659", "sha256_in_prefix": "c2955b7b7126bde70ea7f1749005e27a98c8f2a12368dcf5685ec2d8171c3659", "size_in_bytes": 14631}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-310.pyc", "path_type": "hardlink", "sha256": "1a28f6d69b659675008a01ec02880fd9029dc059812ef081ed6d55754a375a23", "sha256_in_prefix": "1a28f6d69b659675008a01ec02880fd9029dc059812ef081ed6d55754a375a23", "size_in_bytes": 7173}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "45e1670cadfbb93d814a68e545d0c2d6a99ee111745a96ed3dc5cb09db6b3975", "sha256_in_prefix": "45e1670cadfbb93d814a68e545d0c2d6a99ee111745a96ed3dc5cb09db6b3975", "size_in_bytes": 8115}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-310.pyc", "path_type": "hardlink", "sha256": "a8633f8b1a55877867915395fca4a584a7a706785f877d9ad9afedd1c042e3f7", "sha256_in_prefix": "a8633f8b1a55877867915395fca4a584a7a706785f877d9ad9afedd1c042e3f7", "size_in_bytes": 207}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "4cfdedcfdcf1671b0c5227abbdc30a41d43f5ce5a119af5ef849d86f4785efa6", "sha256_in_prefix": "4cfdedcfdcf1671b0c5227abbdc30a41d43f5ce5a119af5ef849d86f4785efa6", "size_in_bytes": 543}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "c8b58bdaa7b74134529ec157944b7e8c6bb7bf55921eca5e12ef5610ec880c0d", "sha256_in_prefix": "c8b58bdaa7b74134529ec157944b7e8c6bb7bf55921eca5e12ef5610ec880c0d", "size_in_bytes": 7214}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "5c41cbd71d1b80bbc4d1b011013df645defb4d8212de22fb327e77642bfd417b", "sha256_in_prefix": "5c41cbd71d1b80bbc4d1b011013df645defb4d8212de22fb327e77642bfd417b", "size_in_bytes": 36720}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "97966dc455ef359384ae439e0eb9e59603027e1ac6def49c43b74dc68316c1da", "sha256_in_prefix": "97966dc455ef359384ae439e0eb9e59603027e1ac6def49c43b74dc68316c1da", "size_in_bytes": 4682}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "c84398922058cb662d956519926022c2fc9a20e517187751a9214ef0705ffe05", "sha256_in_prefix": "c84398922058cb662d956519926022c2fc9a20e517187751a9214ef0705ffe05", "size_in_bytes": 7175}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-310.pyc", "path_type": "hardlink", "sha256": "b7d285aa4cf5eba523720314e4eaad2b2ac6487c679cc8a654e6eb072f9bb4c1", "sha256_in_prefix": "b7d285aa4cf5eba523720314e4eaad2b2ac6487c679cc8a654e6eb072f9bb4c1", "size_in_bytes": 10849}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c83fa3da8fa9ee8beccdbcbf2c4c3de160920d2c7b3796b245104825ae13779", "sha256_in_prefix": "8c83fa3da8fa9ee8beccdbcbf2c4c3de160920d2c7b3796b245104825ae13779", "size_in_bytes": 5997}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-310.pyc", "path_type": "hardlink", "sha256": "81711ba3dcbe5ae07e3057814ceb5fc05ac598b4a9830d60156e793ef4af568a", "sha256_in_prefix": "81711ba3dcbe5ae07e3057814ceb5fc05ac598b4a9830d60156e793ef4af568a", "size_in_bytes": 10743}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-310.pyc", "path_type": "hardlink", "sha256": "48452799bfeeccaf8ee966ad10e989bf67ae0efb42456d0703c573c509a5fbf2", "sha256_in_prefix": "48452799bfeeccaf8ee966ad10e989bf67ae0efb42456d0703c573c509a5fbf2", "size_in_bytes": 1654}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-310.pyc", "path_type": "hardlink", "sha256": "657d179c73eb804e0df317eeca9711a831be0236431b3fb473e4e717b1730778", "sha256_in_prefix": "657d179c73eb804e0df317eeca9711a831be0236431b3fb473e4e717b1730778", "size_in_bytes": 3464}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-310.pyc", "path_type": "hardlink", "sha256": "112fe4fe0322517c2b6c6010b83afdac8e92277533772dd41da22601f5196bc7", "sha256_in_prefix": "112fe4fe0322517c2b6c6010b83afdac8e92277533772dd41da22601f5196bc7", "size_in_bytes": 14484}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-310.pyc", "path_type": "hardlink", "sha256": "cf8955a5543776bd540e473966c9d76099cf56cc7d8d4eabac5616bd059a2c11", "sha256_in_prefix": "cf8955a5543776bd540e473966c9d76099cf56cc7d8d4eabac5616bd059a2c11", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "3aaabcaa53c5ef6eed20cec5a88a75fe72cb03abbcdf94124745c393924f24cf", "sha256_in_prefix": "3aaabcaa53c5ef6eed20cec5a88a75fe72cb03abbcdf94124745c393924f24cf", "size_in_bytes": 10295}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-310.pyc", "path_type": "hardlink", "sha256": "6ea5dece53a6e306abd5594da937bf6afeeb661b08197abf65049b16830f17a3", "sha256_in_prefix": "6ea5dece53a6e306abd5594da937bf6afeeb661b08197abf65049b16830f17a3", "size_in_bytes": 13403}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "ce3303f9fef09b46deca5e16d01f56a1dfe497ea60fbcc7a65206d7bbe9ad875", "sha256_in_prefix": "ce3303f9fef09b46deca5e16d01f56a1dfe497ea60fbcc7a65206d7bbe9ad875", "size_in_bytes": 8083}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-310.pyc", "path_type": "hardlink", "sha256": "f8e94bca94eb8daa917e5c40849786e799bbb3b4eca8b1901d64a737a390c08d", "sha256_in_prefix": "f8e94bca94eb8daa917e5c40849786e799bbb3b4eca8b1901d64a737a390c08d", "size_in_bytes": 5256}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "173cee89cb560495eac964a9e7cf4be630bcd333029fceb5e71197e295d0a7fc", "sha256_in_prefix": "173cee89cb560495eac964a9e7cf4be630bcd333029fceb5e71197e295d0a7fc", "size_in_bytes": 4236}, {"_path": "Lib/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "Lib/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "259bc850a1e27673bfc9d74e692f68697752ad69f240c89f6ad68092fa6c9c85", "sha256_in_prefix": "259bc850a1e27673bfc9d74e692f68697752ad69f240c89f6ad68092fa6c9c85", "size_in_bytes": 2446}, {"_path": "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "e045f2facc92015955ba273207a0b6dacf030b4f57e89d9dd677f729f275e391", "sha256_in_prefix": "e045f2facc92015955ba273207a0b6dacf030b4f57e89d9dd677f729f275e391", "size_in_bytes": 21195}, {"_path": "Lib/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "d798c76cb9820dc9d9ef9276b451720a608feb2176696133573fa5bac69ecabe", "sha256_in_prefix": "d798c76cb9820dc9d9ef9276b451720a608feb2176696133573fa5bac69ecabe", "size_in_bytes": 7844}, {"_path": "Lib/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "38acf0316edfd394d15c0113b0a837e0f5757f7b116c69808f58f5eb9d0907e1", "sha256_in_prefix": "38acf0316edfd394d15c0113b0a837e0f5757f7b116c69808f58f5eb9d0907e1", "size_in_bytes": 49190}, {"_path": "Lib/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "fcb4cb73078d02c0a62af50907d147e200ad779f7f779f57d0c24b1e5c0e6ee1", "sha256_in_prefix": "fcb4cb73078d02c0a62af50907d147e200ad779f7f779f57d0c24b1e5c0e6ee1", "size_in_bytes": 18668}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ad391bd309a44a0873903180038167c112fc34f85bfc28166778b28ba6cd7eaa", "sha256_in_prefix": "ad391bd309a44a0873903180038167c112fc34f85bfc28166778b28ba6cd7eaa", "size_in_bytes": 453}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "ad46d1bd88046ef84dc8e4961fb9d1d09bb094d533b6ceb73ae0e8d64207c537", "sha256_in_prefix": "ad46d1bd88046ef84dc8e4961fb9d1d09bb094d533b6ceb73ae0e8d64207c537", "size_in_bytes": 1874}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "f14a4cf7f12e93ecfb5a7eb318291ab6a7daa34d8e81a343f49aa460e10734e9", "sha256_in_prefix": "f14a4cf7f12e93ecfb5a7eb318291ab6a7daa34d8e81a343f49aa460e10734e9", "size_in_bytes": 4161}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-310.pyc", "path_type": "hardlink", "sha256": "a44e6f7e9a1194d93a5dfb3652a7f3fce33bad61270cf0bb77eb9d975ceac885", "sha256_in_prefix": "a44e6f7e9a1194d93a5dfb3652a7f3fce33bad61270cf0bb77eb9d975ceac885", "size_in_bytes": 3550}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-310.pyc", "path_type": "hardlink", "sha256": "bf01ef510996145cd50c2114c133ce657bd0bed26234acc17b58105df281466c", "sha256_in_prefix": "bf01ef510996145cd50c2114c133ce657bd0bed26234acc17b58105df281466c", "size_in_bytes": 12237}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-310.pyc", "path_type": "hardlink", "sha256": "430b7130e17e931e564495b5cbc2093602594a1825bde6bc5824de75c8c6dab2", "sha256_in_prefix": "430b7130e17e931e564495b5cbc2093602594a1825bde6bc5824de75c8c6dab2", "size_in_bytes": 3915}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "08691f18eea18906b86030766970b4ac2ec3b107af94a2ff9cde721dd62d82aa", "sha256_in_prefix": "08691f18eea18906b86030766970b4ac2ec3b107af94a2ff9cde721dd62d82aa", "size_in_bytes": 4916}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "5853e8634b4841165657bc4534a6903015ca8c5580642225d0b3d09afc2d0c23", "sha256_in_prefix": "5853e8634b4841165657bc4534a6903015ca8c5580642225d0b3d09afc2d0c23", "size_in_bytes": 16836}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "4d6936ba5c4aaf00f87e6eef52fc0da186f42ab50d9ba68e7345f4431ef9db21", "sha256_in_prefix": "4d6936ba5c4aaf00f87e6eef52fc0da186f42ab50d9ba68e7345f4431ef9db21", "size_in_bytes": 9758}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "e7eca37dab0216f87e9fea648a8216355636719436f6abd9a34242c35e430676", "sha256_in_prefix": "e7eca37dab0216f87e9fea648a8216355636719436f6abd9a34242c35e430676", "size_in_bytes": 4644}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-310.pyc", "path_type": "hardlink", "sha256": "1213e3d7c1653098e9d7fb9f9cdb84b90abf4e8dd1b67c242f6a83b87d11bb1e", "sha256_in_prefix": "1213e3d7c1653098e9d7fb9f9cdb84b90abf4e8dd1b67c242f6a83b87d11bb1e", "size_in_bytes": 4848}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-310.pyc", "path_type": "hardlink", "sha256": "6002da36a4600f319f6a0f310c0c71f8a429b5c44a690654af0ebb41e04f8e49", "sha256_in_prefix": "6002da36a4600f319f6a0f310c0c71f8a429b5c44a690654af0ebb41e04f8e49", "size_in_bytes": 2080}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-310.pyc", "path_type": "hardlink", "sha256": "0bb074261a4e1587f65a720856cbb55cc766d0a18c5f13d1c886a20e358cfc8b", "sha256_in_prefix": "0bb074261a4e1587f65a720856cbb55cc766d0a18c5f13d1c886a20e358cfc8b", "size_in_bytes": 10567}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-310.pyc", "path_type": "hardlink", "sha256": "007693fe5d9ee2d835c77dffa31c2465544254611242f476ed43c61a99314eba", "sha256_in_prefix": "007693fe5d9ee2d835c77dffa31c2465544254611242f476ed43c61a99314eba", "size_in_bytes": 17096}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-310.pyc", "path_type": "hardlink", "sha256": "8f1b26edd51b363fc79d3a7afa8ac0e19f20db555fdc0563d961bd0b23d72b6b", "sha256_in_prefix": "8f1b26edd51b363fc79d3a7afa8ac0e19f20db555fdc0563d961bd0b23d72b6b", "size_in_bytes": 2871}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "bd69b104d7a942a4a39a2826ae9ed3250b363039c90cbc048358e2a192693894", "sha256_in_prefix": "bd69b104d7a942a4a39a2826ae9ed3250b363039c90cbc048358e2a192693894", "size_in_bytes": 3421}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-310.pyc", "path_type": "hardlink", "sha256": "484ad32e7aabaff5f8007d9111740a5ff9a1520beb87b32136d105e3cf2b3df7", "sha256_in_prefix": "484ad32e7aabaff5f8007d9111740a5ff9a1520beb87b32136d105e3cf2b3df7", "size_in_bytes": 1826}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-310.pyc", "path_type": "hardlink", "sha256": "e3f98ddd01474fb5f0c443476e7723692f761dcb553a6bed12685fc3af33bf0b", "sha256_in_prefix": "e3f98ddd01474fb5f0c443476e7723692f761dcb553a6bed12685fc3af33bf0b", "size_in_bytes": 5250}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "8dc65f796641ab51d0b9f0e39a661058c14435193d103aaf58a7346b859cb01b", "sha256_in_prefix": "8dc65f796641ab51d0b9f0e39a661058c14435193d103aaf58a7346b859cb01b", "size_in_bytes": 2152}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "42139d3c1b9409eef951d349e058c80eb80a11ae34a7f2c33af55ae17ab2faa9", "sha256_in_prefix": "42139d3c1b9409eef951d349e058c80eb80a11ae34a7f2c33af55ae17ab2faa9", "size_in_bytes": 14466}, {"_path": "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "bad80254deb1214bc0140adcfd9fcee531375f81b4d028ab878e909c539aa280", "sha256_in_prefix": "bad80254deb1214bc0140adcfd9fcee531375f81b4d028ab878e909c539aa280", "size_in_bytes": 5423}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1a2fddd4dcf897a1b3ff15382d17d1551281ae2db65a834f33bb98c97da4b1d9", "sha256_in_prefix": "1a2fddd4dcf897a1b3ff15382d17d1551281ae2db65a834f33bb98c97da4b1d9", "size_in_bytes": 4582}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "0df660bcf9a6dcf4c0777f58ccb790f1f99bc9119a5e8fa79a7533604b5c720d", "sha256_in_prefix": "0df660bcf9a6dcf4c0777f58ccb790f1f99bc9119a5e8fa79a7533604b5c720d", "size_in_bytes": 21686}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "89b6f008474d6d725294359fd3546deb0442b04dc59b0aa2cc6a9ff642e72f92", "sha256_in_prefix": "89b6f008474d6d725294359fd3546deb0442b04dc59b0aa2cc6a9ff642e72f92", "size_in_bytes": 5768}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "30027e4993aa9f486fcb44e721b468cdb7a20d38870b32c61af0892b0db50eef", "sha256_in_prefix": "30027e4993aa9f486fcb44e721b468cdb7a20d38870b32c61af0892b0db50eef", "size_in_bytes": 7727}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "2ca2eb10ba08c361d3936fc510156ab980bbadfa85ce332669648551010d6b45", "sha256_in_prefix": "2ca2eb10ba08c361d3936fc510156ab980bbadfa85ce332669648551010d6b45", "size_in_bytes": 32048}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "70d7bcbf0321c8f27680dea3a2df5c398e4eb943d4bd3ea3f5f205702f857229", "sha256_in_prefix": "70d7bcbf0321c8f27680dea3a2df5c398e4eb943d4bd3ea3f5f205702f857229", "size_in_bytes": 16552}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "775425bff6774f6744c0fde7bceef111e5635c5fd54e7fc95aa1baf13a426b6d", "sha256_in_prefix": "775425bff6774f6744c0fde7bceef111e5635c5fd54e7fc95aa1baf13a426b6d", "size_in_bytes": 5600}, {"_path": "Lib/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "a625fe8c7c91e92d6d7b618a3b5abe8275b715ea8cea633283cb309c75ab36d4", "sha256_in_prefix": "a625fe8c7c91e92d6d7b618a3b5abe8275b715ea8cea633283cb309c75ab36d4", "size_in_bytes": 4912}, {"_path": "Lib/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "aa52ad87be2358b66329ada7c4e6b2ff616e6ba315353ae80296903af6b67707", "sha256_in_prefix": "aa52ad87be2358b66329ada7c4e6b2ff616e6ba315353ae80296903af6b67707", "size_in_bytes": 2595}, {"_path": "Lib/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "14a776bd44953a9d2ba5551eaf86e3e83f78f9fcb1c85f072718ad46564573d7", "sha256_in_prefix": "14a776bd44953a9d2ba5551eaf86e3e83f78f9fcb1c85f072718ad46564573d7", "size_in_bytes": 13008}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "9974f09e1a3d7b8918202288e7e642e2b57b253d1ce8cf02f2355801fa1f785d", "sha256_in_prefix": "9974f09e1a3d7b8918202288e7e642e2b57b253d1ce8cf02f2355801fa1f785d", "size_in_bytes": 30122}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "4dc5859587793500de7ea89056790b3de14fd27d5b7b17e1e880ecf24f475d30", "sha256_in_prefix": "4dc5859587793500de7ea89056790b3de14fd27d5b7b17e1e880ecf24f475d30", "size_in_bytes": 2810}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "d5016e91e009c52b5c93c747889cdeea9e170924eeb4194f108311e6fcca972e", "sha256_in_prefix": "d5016e91e009c52b5c93c747889cdeea9e170924eeb4194f108311e6fcca972e", "size_in_bytes": 1251}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "a4a2cd135ae7a9da12a3c6eaf5e7d06d0b90a6b8394c6b30169bca91ad45dc58", "sha256_in_prefix": "a4a2cd135ae7a9da12a3c6eaf5e7d06d0b90a6b8394c6b30169bca91ad45dc58", "size_in_bytes": 8330}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "430f0aac2db899c21e244bdc04d28848ca62ef99e94a6ea3cd6e813b303d1bb8", "sha256_in_prefix": "430f0aac2db899c21e244bdc04d28848ca62ef99e94a6ea3cd6e813b303d1bb8", "size_in_bytes": 1937}, {"_path": "Lib/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "14d4b3edc448e10d6fee80c7a1503104029da3283a6991dd6076b6e53ac79b6e", "sha256_in_prefix": "14d4b3edc448e10d6fee80c7a1503104029da3283a6991dd6076b6e53ac79b6e", "size_in_bytes": 18837}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "b40d2bcfea679742cf326789abbc8b94cc0a04cbf02be1699c8c2589890afa21", "sha256_in_prefix": "b40d2bcfea679742cf326789abbc8b94cc0a04cbf02be1699c8c2589890afa21", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "08114b066672ab68b8d3d7f0c259ad0af1ea985f4a01f39f89da92e217409a79", "sha256_in_prefix": "08114b066672ab68b8d3d7f0c259ad0af1ea985f4a01f39f89da92e217409a79", "size_in_bytes": 839}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "a8884684dc5cbbd12d4c67ea7908f621a0c3de796e7fc61b596c4e2a1cfe7945", "sha256_in_prefix": "a8884684dc5cbbd12d4c67ea7908f621a0c3de796e7fc61b596c4e2a1cfe7945", "size_in_bytes": 1889}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "d4b40ed29f80c0348dccb264fca3c82a9eb67a20e99066787cc32cd8dde8f78c", "sha256_in_prefix": "d4b40ed29f80c0348dccb264fca3c82a9eb67a20e99066787cc32cd8dde8f78c", "size_in_bytes": 11891}, {"_path": "Lib/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "Lib/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "Lib/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "Lib/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "f4d39a17b4f3df6b80f6d97548c9307b96dbf1833a844b68d353c6beea3eb835", "sha256_in_prefix": "f4d39a17b4f3df6b80f6d97548c9307b96dbf1833a844b68d353c6beea3eb835", "size_in_bytes": 51529}, {"_path": "Lib/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "6d9ddc2f5629998547258120c3c50cf2f96c2cc2297805ea8ba203495f58aa1c", "sha256_in_prefix": "6d9ddc2f5629998547258120c3c50cf2f96c2cc2297805ea8ba203495f58aa1c", "size_in_bytes": 3325}, {"_path": "Lib/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "37a1b288cb3fab13320e370e5ee2918842026f315c5576d80f70ee0fdfe10f99", "sha256_in_prefix": "37a1b288cb3fab13320e370e5ee2918842026f315c5576d80f70ee0fdfe10f99", "size_in_bytes": 10755}, {"_path": "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "Lib/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "Lib/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "0d7ddd5a70d3026124e5eed290661d5fa4491c50b36451fcaad226eea14069b1", "sha256_in_prefix": "0d7ddd5a70d3026124e5eed290661d5fa4491c50b36451fcaad226eea14069b1", "size_in_bytes": 13567}, {"_path": "Lib/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "Lib/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "902b440052603e94995dba33ad57ee07b078a2661486bfa12daba4a6e5c65d88", "sha256_in_prefix": "902b440052603e94995dba33ad57ee07b078a2661486bfa12daba4a6e5c65d88", "size_in_bytes": 3634}, {"_path": "Lib/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "33ff692920a02176bf7adbc8cf387600ebaf04b008581ef45a69f4113e2168a9", "sha256_in_prefix": "33ff692920a02176bf7adbc8cf387600ebaf04b008581ef45a69f4113e2168a9", "size_in_bytes": 19266}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ab8febbc1e7fc2393b7719919e243fa535f27994a52fce5c15decb08286eda3a", "sha256_in_prefix": "ab8febbc1e7fc2393b7719919e243fa535f27994a52fce5c15decb08286eda3a", "size_in_bytes": 1477}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-310.pyc", "path_type": "hardlink", "sha256": "94c2557eff75efdc394860d073dda3bab9499c641e026b4fff190737a4f9c2eb", "sha256_in_prefix": "94c2557eff75efdc394860d073dda3bab9499c641e026b4fff190737a4f9c2eb", "size_in_bytes": 5078}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "936cf6565c1d38351570fc76eb12d6eb7889922c6aa36bcf2f701537146211a1", "sha256_in_prefix": "936cf6565c1d38351570fc76eb12d6eb7889922c6aa36bcf2f701537146211a1", "size_in_bytes": 10674}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "d8a55495f1426bfa3c0e51ddddf054b5a4fe8bf62ccc6230e2664220ad77bab5", "sha256_in_prefix": "d8a55495f1426bfa3c0e51ddddf054b5a4fe8bf62ccc6230e2664220ad77bab5", "size_in_bytes": 1286}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-310.pyc", "path_type": "hardlink", "sha256": "88e524d5afdf26a391420600e38d6f782b77b0d3c02a88d9acd86cd6369142b2", "sha256_in_prefix": "88e524d5afdf26a391420600e38d6f782b77b0d3c02a88d9acd86cd6369142b2", "size_in_bytes": 2082}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-310.pyc", "path_type": "hardlink", "sha256": "cf2b09f691e80d6508a517c78b5a9ee77a0705e53674ec1e268bce836e69c5e4", "sha256_in_prefix": "cf2b09f691e80d6508a517c78b5a9ee77a0705e53674ec1e268bce836e69c5e4", "size_in_bytes": 2992}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-310.pyc", "path_type": "hardlink", "sha256": "2d7d343105f9e196af5f513b15976ff6ac1c365d02343536d20404a1bc4fce4a", "sha256_in_prefix": "2d7d343105f9e196af5f513b15976ff6ac1c365d02343536d20404a1bc4fce4a", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "1b3617ffa8908a8136cb8f4b8448df14bc2aef23f3f03244914114529139c10e", "sha256_in_prefix": "1b3617ffa8908a8136cb8f4b8448df14bc2aef23f3f03244914114529139c10e", "size_in_bytes": 3733}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "790c029f57c0cacb35c525047ef3e6500ab63e9ab39e796d15fcd813f778f0f8", "sha256_in_prefix": "790c029f57c0cacb35c525047ef3e6500ab63e9ab39e796d15fcd813f778f0f8", "size_in_bytes": 13679}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "3732f8b60dacaad3c1df26a53d4bb677edefe79d000268a0b28ed7c1c2965928", "sha256_in_prefix": "3732f8b60dacaad3c1df26a53d4bb677edefe79d000268a0b28ed7c1c2965928", "size_in_bytes": 5559}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "262919f984dab4abb52ed6bda769bb37dc4a852d26b29bf6c1d2b3c69a84b97a", "sha256_in_prefix": "262919f984dab4abb52ed6bda769bb37dc4a852d26b29bf6c1d2b3c69a84b97a", "size_in_bytes": 3095}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_ccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "3fc1ccd2fa8c3e4d8249ac76cacb01ce5870f1843134ba6e02226809040893de", "sha256_in_prefix": "3fc1ccd2fa8c3e4d8249ac76cacb01ce5870f1843134ba6e02226809040893de", "size_in_bytes": 2735}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-310.pyc", "path_type": "hardlink", "sha256": "1eb53fc02abf6ce760bc7d59be295f86c7fed238be1fc4400133327542f2819c", "sha256_in_prefix": "1eb53fc02abf6ce760bc7d59be295f86c7fed238be1fc4400133327542f2819c", "size_in_bytes": 4403}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-310.pyc", "path_type": "hardlink", "sha256": "d6fc88f120987aa34283b2b2a02eede987af3a4c9b88a01be46272f2729d4cb5", "sha256_in_prefix": "d6fc88f120987aa34283b2b2a02eede987af3a4c9b88a01be46272f2729d4cb5", "size_in_bytes": 1299}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-310.pyc", "path_type": "hardlink", "sha256": "059b886923cbe43729459ea275bc6791324012ddb7aed6abf3cc677f65522f27", "sha256_in_prefix": "059b886923cbe43729459ea275bc6791324012ddb7aed6abf3cc677f65522f27", "size_in_bytes": 4055}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-310.pyc", "path_type": "hardlink", "sha256": "b1c246154f53d231d76a0c66bbd556a2aefbe36a57d4787313e74e155b9b446d", "sha256_in_prefix": "b1c246154f53d231d76a0c66bbd556a2aefbe36a57d4787313e74e155b9b446d", "size_in_bytes": 3013}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-310.pyc", "path_type": "hardlink", "sha256": "04d7dc72381103e27393c33af77191e1e1edeedfbcbef9689c3b43e2ef2b8c7e", "sha256_in_prefix": "04d7dc72381103e27393c33af77191e1e1edeedfbcbef9689c3b43e2ef2b8c7e", "size_in_bytes": 3952}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cygwinccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "1ec9e1a1987c1a49f455b8c7a3b825bc252a06280433dc3f167fc7755e1ea3d9", "sha256_in_prefix": "1ec9e1a1987c1a49f455b8c7a3b825bc252a06280433dc3f167fc7755e1ea3d9", "size_in_bytes": 2919}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "4d03d78604a2dc59c38e1521c686a1a3c4d97229fa2b92827bc31722a6687a6c", "sha256_in_prefix": "4d03d78604a2dc59c38e1521c686a1a3c4d97229fa2b92827bc31722a6687a6c", "size_in_bytes": 4933}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "366d5387aaf4a96e8980430b911e121fe40aaf1c3ac0d0c0b66b7419242a243f", "sha256_in_prefix": "366d5387aaf4a96e8980430b911e121fe40aaf1c3ac0d0c0b66b7419242a243f", "size_in_bytes": 16746}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "97280351f18e8923ac867c15dfa7da801b64b660e7fcde35ec68a8094ae3bf47", "sha256_in_prefix": "97280351f18e8923ac867c15dfa7da801b64b660e7fcde35ec68a8094ae3bf47", "size_in_bytes": 2584}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "ca4872250382c46f5ae0c0ce798909a7fbca89b47053493b00742e9253e42809", "sha256_in_prefix": "ca4872250382c46f5ae0c0ce798909a7fbca89b47053493b00742e9253e42809", "size_in_bytes": 3475}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-310.pyc", "path_type": "hardlink", "sha256": "b2bd27b05a639c39ecd4e0b02978d81c5841e10c2e208974f81d09a2515df72c", "sha256_in_prefix": "b2bd27b05a639c39ecd4e0b02978d81c5841e10c2e208974f81d09a2515df72c", "size_in_bytes": 8286}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "ded16a2ab23c5a732a370941183ddc81dc5a5b161e94497105ce152bc57b9ce5", "sha256_in_prefix": "ded16a2ab23c5a732a370941183ddc81dc5a5b161e94497105ce152bc57b9ce5", "size_in_bytes": 7383}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-310.pyc", "path_type": "hardlink", "sha256": "449bcb431be4814764a0a7bb33c355c85dd90688afd04bd2bc42c86b8ea8b972", "sha256_in_prefix": "449bcb431be4814764a0a7bb33c355c85dd90688afd04bd2bc42c86b8ea8b972", "size_in_bytes": 1838}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-310.pyc", "path_type": "hardlink", "sha256": "6c1b5c435ce338d6f69e4f6a452617973a3889c1732b377f2351f43f5e2fc80f", "sha256_in_prefix": "6c1b5c435ce338d6f69e4f6a452617973a3889c1732b377f2351f43f5e2fc80f", "size_in_bytes": 1138}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-310.pyc", "path_type": "hardlink", "sha256": "a6bc8dc613ff9857d2c6a91a8073b65f787128ac3b234433bc6025b17853730f", "sha256_in_prefix": "a6bc8dc613ff9857d2c6a91a8073b65f787128ac3b234433bc6025b17853730f", "size_in_bytes": 3101}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "5f7e20259a4c6f3e407447c0ef621105a984bdd2b8e02949736fa18735534654", "sha256_in_prefix": "5f7e20259a4c6f3e407447c0ef621105a984bdd2b8e02949736fa18735534654", "size_in_bytes": 1655}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-310.pyc", "path_type": "hardlink", "sha256": "f3d1ce418d856cc8915bb8885851b452ecd93dfbaad3826ee30e0b0d76ea0bc1", "sha256_in_prefix": "f3d1ce418d856cc8915bb8885851b452ecd93dfbaad3826ee30e0b0d76ea0bc1", "size_in_bytes": 689}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_mingwccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "7465d7a3393b9ab03851352abba7b372bfe9570b3e7dee7f36643d590c0cf7f3", "sha256_in_prefix": "7465d7a3393b9ab03851352abba7b372bfe9570b3e7dee7f36643d590c0cf7f3", "size_in_bytes": 2551}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-310.pyc", "path_type": "hardlink", "sha256": "f7116c6c63f6db379804abe0cb18ea15194343a8c636479b0a5f4f7e50da8e18", "sha256_in_prefix": "f7116c6c63f6db379804abe0cb18ea15194343a8c636479b0a5f4f7e50da8e18", "size_in_bytes": 4174}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_msvccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "a7d697563c01892882a4c47f746e8a3b3c61c334b79c8cb8a254ef3459cd6585", "sha256_in_prefix": "a7d697563c01892882a4c47f746e8a3b3c61c334b79c8cb8a254ef3459cd6585", "size_in_bytes": 5164}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "4502d76bab405285544ba386197ae5d648f566031f17cc1866b115f98f5ab750", "sha256_in_prefix": "4502d76bab405285544ba386197ae5d648f566031f17cc1866b115f98f5ab750", "size_in_bytes": 11182}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-310.pyc", "path_type": "hardlink", "sha256": "594b6c6179f6486d3f75bfbc9a299f844ecc17b688d46d0507ed2531d4b74652", "sha256_in_prefix": "594b6c6179f6486d3f75bfbc9a299f844ecc17b688d46d0507ed2531d4b74652", "size_in_bytes": 3584}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-310.pyc", "path_type": "hardlink", "sha256": "3fa007af20e70ac8bb8482a48cb2f60bd41ebcf70a19d58b6fd915a341cefb32", "sha256_in_prefix": "3fa007af20e70ac8bb8482a48cb2f60bd41ebcf70a19d58b6fd915a341cefb32", "size_in_bytes": 10851}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-310.pyc", "path_type": "hardlink", "sha256": "5932892dcecfdd8a0fca51b90dcc8c6ff3e4d2adc52e7d5bc4a5526c4a3de943", "sha256_in_prefix": "5932892dcecfdd8a0fca51b90dcc8c6ff3e4d2adc52e7d5bc4a5526c4a3de943", "size_in_bytes": 2145}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_unixccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "801e7ed72ac16c1349bfb8efbf91ea77cfcb7e8dfa785be822990060b4e8707f", "sha256_in_prefix": "801e7ed72ac16c1349bfb8efbf91ea77cfcb7e8dfa785be822990060b4e8707f", "size_in_bytes": 9322}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "62be390412b51893495b1eda83ad8619b8926a09461d5f46eaed754615155885", "sha256_in_prefix": "62be390412b51893495b1eda83ad8619b8926a09461d5f46eaed754615155885", "size_in_bytes": 7873}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-310.pyc", "path_type": "hardlink", "sha256": "a24781f579ef3a30b9ad8a6445a10c9b938590d6de72b5dfe14a8ff7123b4315", "sha256_in_prefix": "a24781f579ef3a30b9ad8a6445a10c9b938590d6de72b5dfe14a8ff7123b4315", "size_in_bytes": 2448}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-310.pyc", "path_type": "hardlink", "sha256": "73e945300e6d43cbabb92bde444419463f832cb790629cd9118af62f123cf8b9", "sha256_in_prefix": "73e945300e6d43cbabb92bde444419463f832cb790629cd9118af62f123cf8b9", "size_in_bytes": 163}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "529a8e978a433f623f7d439ecd795e8b859d8cf81fcf5841bd157b7c95b51cfc", "sha256_in_prefix": "529a8e978a433f623f7d439ecd795e8b859d8cf81fcf5841bd157b7c95b51cfc", "size_in_bytes": 499}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "536ad961db3a7c6eaacc94d440611fcd375912a0d05515c76fd32d543dee5a02", "sha256_in_prefix": "536ad961db3a7c6eaacc94d440611fcd375912a0d05515c76fd32d543dee5a02", "size_in_bytes": 157}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "5e7c6a3eb4595ff3c92b7ebc10021a8709f44d03bc4fa2134963ed0bfd513410", "sha256_in_prefix": "5e7c6a3eb4595ff3c92b7ebc10021a8709f44d03bc4fa2134963ed0bfd513410", "size_in_bytes": 572}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "23a1d4cd7e8c8035b6e6fe01dfd5c12ea3df553f12c863a5d56fb7d9e84864e3", "sha256_in_prefix": "23a1d4cd7e8c8035b6e6fe01dfd5c12ea3df553f12c863a5d56fb7d9e84864e3", "size_in_bytes": 19954}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_ccompiler.py", "path_type": "hardlink", "sha256": "b8bbdd69a135f052769d31fe310ebb20f927a98686d4e43ecf4d28934263c0c0", "sha256_in_prefix": "b8bbdd69a135f052769d31fe310ebb20f927a98686d4e43ecf4d28934263c0c0", "size_in_bytes": 3026}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cygwinccompiler.py", "path_type": "hardlink", "sha256": "8aac6c0f2d19e594d183133c011ccf5da922b50a1dd95f1a1b9a9eb7f279b538", "sha256_in_prefix": "8aac6c0f2d19e594d183133c011ccf5da922b50a1dd95f1a1b9a9eb7f279b538", "size_in_bytes": 2753}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_mingwccompiler.py", "path_type": "hardlink", "sha256": "98197c5bc4083b6c72e1e3a3e9a0045689b89686f0a4733e1ef154217bbaab47", "sha256_in_prefix": "98197c5bc4083b6c72e1e3a3e9a0045689b89686f0a4733e1ef154217bbaab47", "size_in_bytes": 2202}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_msvccompiler.py", "path_type": "hardlink", "sha256": "c54adfc82c023b9ec312cc5ca0beacf981b760865196562c2ae6a065b04f149d", "sha256_in_prefix": "c54adfc82c023b9ec312cc5ca0beacf981b760865196562c2ae6a065b04f149d", "size_in_bytes": 4301}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "227b1b534f5a795749b63f10cb04449d466e577d9bbe2e3b791987de2590c249", "sha256_in_prefix": "227b1b534f5a795749b63f10cb04449d466e577d9bbe2e3b791987de2590c249", "size_in_bytes": 15058}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_unixccompiler.py", "path_type": "hardlink", "sha256": "5b0084d333d8344da75520ee4d8270b51f81a66134b6e4c99423507de57f24e2", "sha256_in_prefix": "5b0084d333d8344da75520ee4d8270b51f81a66134b6e4c99423507de57f24e2", "size_in_bytes": 11835}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "4091cd71088cb5670e4385b3ba9cc8bf59d0c0110da3e6cd91e542495993e099", "sha256_in_prefix": "4091cd71088cb5670e4385b3ba9cc8bf59d0c0110da3e6cd91e542495993e099", "size_in_bytes": 15437}, {"_path": "Lib/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "6d785fb6ecfad68b331afc335d4b6ff64b5e18d7beed89854c4bb7dc3ea94217", "sha256_in_prefix": "6d785fb6ecfad68b331afc335d4b6ff64b5e18d7beed89854c4bb7dc3ea94217", "size_in_bytes": 17493}, {"_path": "Lib/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "6dbd9d4281a7b2fe0b9a84017e3843b1a3a9b7fa7947bcbfdbc975725b661bde", "sha256_in_prefix": "6dbd9d4281a7b2fe0b9a84017e3843b1a3a9b7fa7947bcbfdbc975725b661bde", "size_in_bytes": 6589}, {"_path": "Lib/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "Lib/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "Lib/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "b7e49e5dcd23536c1e418f41037a869514e1cc1343d6860ae47a73835ff9df78", "sha256_in_prefix": "b7e49e5dcd23536c1e418f41037a869514e1cc1343d6860ae47a73835ff9df78", "size_in_bytes": 4536}, {"_path": "Lib/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "sha256_in_prefix": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "size_in_bytes": 2685}, {"_path": "Lib/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "sha256_in_prefix": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "sha256_in_prefix": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "size_in_bytes": 1496}, {"_path": "Lib/site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "c35d1bbf4e8d4a938587008d0b715091e54dcdc899bc8ccd669f0d5f7d356e1b", "sha256_in_prefix": "c35d1bbf4e8d4a938587008d0b715091e54dcdc899bc8ccd669f0d5f7d356e1b", "size_in_bytes": 4855}, {"_path": "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-310.pyc", "path_type": "hardlink", "sha256": "6910b96f4e667921304d4e5906943c875bcd466fbcffbd46b093dfc1de82d429", "sha256_in_prefix": "6910b96f4e667921304d4e5906943c875bcd466fbcffbd46b093dfc1de82d429", "size_in_bytes": 100301}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "00d65831e4d987dbe19d7ff8e2af323ad21f5a7ab0edb751aa638186b0446892", "sha256_in_prefix": "00d65831e4d987dbe19d7ff8e2af323ad21f5a7ab0edb751aa638186b0446892", "size_in_bytes": 348}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-310.pyc", "path_type": "hardlink", "sha256": "3006bef49b6c97f79baca7192d87bc06d7bf79fa7e8b917a770d1b79f19f730f", "sha256_in_prefix": "3006bef49b6c97f79baca7192d87bc06d7bf79fa7e8b917a770d1b79f19f730f", "size_in_bytes": 4141}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-310.pyc", "path_type": "hardlink", "sha256": "11c4e4fc93a0fc35f53a53ed704877ce325e55b0122bb1511967eaeb608ea05b", "sha256_in_prefix": "11c4e4fc93a0fc35f53a53ed704877ce325e55b0122bb1511967eaeb608ea05b", "size_in_bytes": 993}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-310.pyc", "path_type": "hardlink", "sha256": "a32ca54dcaa5bfdf7884fcf9eacc5dc49e4b8b4adbcf37b43ad90f2c7621585c", "sha256_in_prefix": "a32ca54dcaa5bfdf7884fcf9eacc5dc49e4b8b4adbcf37b43ad90f2c7621585c", "size_in_bytes": 1631}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-310.pyc", "path_type": "hardlink", "sha256": "3727607e5a4124dff5dcd3c13a3ea28ffd8b745e49bec03998500d969ff69f69", "sha256_in_prefix": "3727607e5a4124dff5dcd3c13a3ea28ffd8b745e49bec03998500d969ff69f69", "size_in_bytes": 8348}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "05faaae53ce928de4affd6d12a9c6992fdf6c700e5603a2efb76081b65e7b244", "sha256_in_prefix": "05faaae53ce928de4affd6d12a9c6992fdf6c700e5603a2efb76081b65e7b244", "size_in_bytes": 378}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "366dac1b9edf8e0cc9f3e6a8478a95d908602e8f971cdb5946946ab5ba838250", "sha256_in_prefix": "366dac1b9edf8e0cc9f3e6a8478a95d908602e8f971cdb5946946ab5ba838250", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b1a1de2052b03b4c52d41846ac1694e35ffc66a29abb3e0b5a236242b4d763db", "sha256_in_prefix": "b1a1de2052b03b4c52d41846ac1694e35ffc66a29abb3e0b5a236242b4d763db", "size_in_bytes": 71890}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "27911b0263785e9494e2938d1dad8390ee610869aeb4ffc36d981f8fbbb7c4a9", "sha256_in_prefix": "27911b0263785e9494e2938d1dad8390ee610869aeb4ffc36d981f8fbbb7c4a9", "size_in_bytes": 232}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f28488908fcb35150304d34b06fa0ac5c87db521ac554c15839949b31d27f807", "sha256_in_prefix": "f28488908fcb35150304d34b06fa0ac5c87db521ac554c15839949b31d27f807", "size_in_bytes": 166}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-310.pyc", "path_type": "hardlink", "sha256": "ccc861b34f083d0b1b8597d1b25518c6c3a9d0ead2803b241345587f11a17f8c", "sha256_in_prefix": "ccc861b34f083d0b1b8597d1b25518c6c3a9d0ead2803b241345587f11a17f8c", "size_in_bytes": 748}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "92f1bea48abdee6746b820055bf0752028227bfb9b7003b10642867240f75708", "sha256_in_prefix": "92f1bea48abdee6746b820055bf0752028227bfb9b7003b10642867240f75708", "size_in_bytes": 40228}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-310.pyc", "path_type": "hardlink", "sha256": "b7050f43adf3b7944bb3849cbe4ea6f09c2d428b9fdf7a1a469a36a32bd0f784", "sha256_in_prefix": "b7050f43adf3b7944bb3849cbe4ea6f09c2d428b9fdf7a1a469a36a32bd0f784", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-310.pyc", "path_type": "hardlink", "sha256": "bbb9ba081156b831d8750d248b2c8ddeae9ee7c04c9ef20e3216242cf9df32b7", "sha256_in_prefix": "bbb9ba081156b831d8750d248b2c8ddeae9ee7c04c9ef20e3216242cf9df32b7", "size_in_bytes": 1536}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "8123384d467374c52ff3243ee5626d496ad7cf096d5413c2c13f13f062438033", "sha256_in_prefix": "8123384d467374c52ff3243ee5626d496ad7cf096d5413c2c13f13f062438033", "size_in_bytes": 1868}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-310.pyc", "path_type": "hardlink", "sha256": "968f38e5ae1117be5949013220bfb125e3d531aded9838eea8468fb0dfac4d74", "sha256_in_prefix": "968f38e5ae1117be5949013220bfb125e3d531aded9838eea8468fb0dfac4d74", "size_in_bytes": 3126}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-310.pyc", "path_type": "hardlink", "sha256": "fe3072e74bcb7ec4b15602a5bc8af71a8f525e943546d37c2672e7652c3d542f", "sha256_in_prefix": "fe3072e74bcb7ec4b15602a5bc8af71a8f525e943546d37c2672e7652c3d542f", "size_in_bytes": 2007}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "6b345ddbbce08c99cca73979aa226dd223e5d6c3f73db3d0892acfcfa63f5bf8", "sha256_in_prefix": "6b345ddbbce08c99cca73979aa226dd223e5d6c3f73db3d0892acfcfa63f5bf8", "size_in_bytes": 3333}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-310.pyc", "path_type": "hardlink", "sha256": "8b0358cd423f562bba870e76a99413003f2a866bde77ab33e4592c739bb106b7", "sha256_in_prefix": "8b0358cd423f562bba870e76a99413003f2a866bde77ab33e4592c739bb106b7", "size_in_bytes": 3059}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-310.pyc", "path_type": "hardlink", "sha256": "b720fe5a2a373f169f655c8996798838c00d1d57d14cbca5c9d104e0dbf779a3", "sha256_in_prefix": "b720fe5a2a373f169f655c8996798838c00d1d57d14cbca5c9d104e0dbf779a3", "size_in_bytes": 828}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "fe5f7faea62c5874d02161178467c5175d2a1527311201861c078248658c6ed5", "sha256_in_prefix": "fe5f7faea62c5874d02161178467c5175d2a1527311201861c078248658c6ed5", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-310.pyc", "path_type": "hardlink", "sha256": "053ecb08e0a0b83d9c709c0b7d6d6bb938ded60702ba450b45f74c0e925dd67a", "sha256_in_prefix": "053ecb08e0a0b83d9c709c0b7d6d6bb938ded60702ba450b45f74c0e925dd67a", "size_in_bytes": 1009}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "bfb986b512d0a3ad116fcee0e2c8d70e5ea04c3f700249855892ae00e78fc183", "sha256_in_prefix": "bfb986b512d0a3ad116fcee0e2c8d70e5ea04c3f700249855892ae00e78fc183", "size_in_bytes": 1162}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e96c735578637eee345a0a3d5023d0d5a29b6e026f76e401159dd3aa69cce09", "sha256_in_prefix": "2e96c735578637eee345a0a3d5023d0d5a29b6e026f76e401159dd3aa69cce09", "size_in_bytes": 74038}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "89f0ca35bd5ba1682d46f830a81b039a19df539b0e16f861670abc4d298ae23d", "sha256_in_prefix": "89f0ca35bd5ba1682d46f830a81b039a19df539b0e16f861670abc4d298ae23d", "size_in_bytes": 156}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-310.pyc", "path_type": "hardlink", "sha256": "87d2257293f1558f7b1a71edcca879e4c9250cc90e3f8b21f0bf03058be113c2", "sha256_in_prefix": "87d2257293f1558f7b1a71edcca879e4c9250cc90e3f8b21f0bf03058be113c2", "size_in_bytes": 282}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-310.pyc", "path_type": "hardlink", "sha256": "ece78f5797cead73fd807d0139acfbe0f50115348873abd37a2d9f927ca222a9", "sha256_in_prefix": "ece78f5797cead73fd807d0139acfbe0f50115348873abd37a2d9f927ca222a9", "size_in_bytes": 11057}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "e4d290204129f9553ee5daf32ee321f3da066883bb18ea99f7e77338b084dfe9", "sha256_in_prefix": "e4d290204129f9553ee5daf32ee321f3da066883bb18ea99f7e77338b084dfe9", "size_in_bytes": 32198}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a4da45a8169b18002652f0c7d4986a1839178af4d70223def6fbd34f34ec0816", "sha256_in_prefix": "a4da45a8169b18002652f0c7d4986a1839178af4d70223def6fbd34f34ec0816", "size_in_bytes": 19214}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "db27e3a4d293d240016e88975fcba916e17f3d29c7a93d1f1a0ce09c1037d398", "sha256_in_prefix": "db27e3a4d293d240016e88975fcba916e17f3d29c7a93d1f1a0ce09c1037d398", "size_in_bytes": 20433}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-310.pyc", "path_type": "hardlink", "sha256": "23600618104889f69c062e9ed08f979bd5143a17d342624fd54376bca0c27d8a", "sha256_in_prefix": "23600618104889f69c062e9ed08f979bd5143a17d342624fd54376bca0c27d8a", "size_in_bytes": 869}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-310.pyc", "path_type": "hardlink", "sha256": "c8b660e3d755c7a70e52a72d34c8d5fcb75e5c17d9a5939961a6972c98ed6834", "sha256_in_prefix": "c8b660e3d755c7a70e52a72d34c8d5fcb75e5c17d9a5939961a6972c98ed6834", "size_in_bytes": 1091}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-310.pyc", "path_type": "hardlink", "sha256": "656a9d5e4e87a168db5e77a226586a9a632cd54d96ccc4413193eb3f8c1c8e56", "sha256_in_prefix": "656a9d5e4e87a168db5e77a226586a9a632cd54d96ccc4413193eb3f8c1c8e56", "size_in_bytes": 639}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c571528593fec1c7e830eb94466de588dacb51d02a7a31753c1620f62bdce30", "sha256_in_prefix": "8c571528593fec1c7e830eb94466de588dacb51d02a7a31753c1620f62bdce30", "size_in_bytes": 295}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-310.pyc", "path_type": "hardlink", "sha256": "c2b00c68a6965eb5177b5af46357527dcae94f910afd556d2db65cfd6889c73b", "sha256_in_prefix": "c2b00c68a6965eb5177b5af46357527dcae94f910afd556d2db65cfd6889c73b", "size_in_bytes": 295}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f9b66eb8f9e853298eea19fe8611f0dd5098423828752c54237ccc1bdd3191ae", "sha256_in_prefix": "f9b66eb8f9e853298eea19fe8611f0dd5098423828752c54237ccc1bdd3191ae", "size_in_bytes": 298}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-310.pyc", "path_type": "hardlink", "sha256": "7ff81a58783474e138999f6077386fffea18640eb722cd7100bd3c97c428794d", "sha256_in_prefix": "7ff81a58783474e138999f6077386fffea18640eb722cd7100bd3c97c428794d", "size_in_bytes": 138258}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-310.pyc", "path_type": "hardlink", "sha256": "e1b95a6fc06c87099857508bc342013754f7d07fefcf3bcbe78581c8ed8b0ca9", "sha256_in_prefix": "e1b95a6fc06c87099857508bc342013754f7d07fefcf3bcbe78581c8ed8b0ca9", "size_in_bytes": 29191}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a52e2f7e724745914395d497937f49969067bb9f149bf1c8d51e92487a66e9e3", "sha256_in_prefix": "a52e2f7e724745914395d497937f49969067bb9f149bf1c8d51e92487a66e9e3", "size_in_bytes": 481}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "hardlink", "sha256": "1918db9eed9db0ae5267ab7f3e614e5adf6a8c9e51ae803e7dd72627957c3e9e", "sha256_in_prefix": "1918db9eed9db0ae5267ab7f3e614e5adf6a8c9e51ae803e7dd72627957c3e9e", "size_in_bytes": 3356}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "ef90913c544ed63c0c9d741d8b941ef3f0ff7f834dff23c0b800bcdd5fb45c63", "sha256_in_prefix": "ef90913c544ed63c0c9d741d8b941ef3f0ff7f834dff23c0b800bcdd5fb45c63", "size_in_bytes": 6546}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "e08ff5e61ec6db1e126376609613d005197feb79a8d12839a6457c05d3f0f381", "sha256_in_prefix": "e08ff5e61ec6db1e126376609613d005197feb79a8d12839a6457c05d3f0f381", "size_in_bytes": 3405}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "c5482af10ff783c612cbad30242e8705ce13187d83a29d885786eaa35c4e425a", "sha256_in_prefix": "c5482af10ff783c612cbad30242e8705ce13187d83a29d885786eaa35c4e425a", "size_in_bytes": 9221}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "hardlink", "sha256": "4cd149ad092f2370d186085b445e675982eccc67cda6c0fc5e6f32f8bfbaa6b2", "sha256_in_prefix": "4cd149ad092f2370d186085b445e675982eccc67cda6c0fc5e6f32f8bfbaa6b2", "size_in_bytes": 2663}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "hardlink", "sha256": "796c740482b458794919bd7ef86fec45efe550b4cd607355ac01671842d1d9c9", "sha256_in_prefix": "796c740482b458794919bd7ef86fec45efe550b4cd607355ac01671842d1d9c9", "size_in_bytes": 5875}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "d7a29b6de38ed0835c442855cb5267b08e01be817b96ace78ebaf794b3d78314", "sha256_in_prefix": "d7a29b6de38ed0835c442855cb5267b08e01be817b96ace78ebaf794b3d78314", "size_in_bytes": 7842}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "1210d44448eba6cac5e2e638de95d3ed7e90ea3ca2795084b8fb28cf34ecddd7", "sha256_in_prefix": "1210d44448eba6cac5e2e638de95d3ed7e90ea3ca2795084b8fb28cf34ecddd7", "size_in_bytes": 18706}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "hardlink", "sha256": "ca4803049d93c37e844c9647aac976d7d3ae6c0f458325a1dc014fe06a636b42", "sha256_in_prefix": "ca4803049d93c37e844c9647aac976d7d3ae6c0f458325a1dc014fe06a636b42", "size_in_bytes": 2875}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "hardlink", "sha256": "e708182fab3dfc79d6d0303b8b1d582727976b344fb62a892d3bececc563e0fd", "sha256_in_prefix": "e708182fab3dfc79d6d0303b8b1d582727976b344fb62a892d3bececc563e0fd", "size_in_bytes": 31349}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "598987b455dfb72afcb244d9fbc268291c2299e4b3f8e735ca5b4bb59750393b", "sha256_in_prefix": "598987b455dfb72afcb244d9fbc268291c2299e4b3f8e735ca5b4bb59750393b", "size_in_bytes": 15175}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "68447b857910dea9e8fa1b29dfc820e00219054fb060806eb27fcfdfeba5e241", "sha256_in_prefix": "68447b857910dea9e8fa1b29dfc820e00219054fb060806eb27fcfdfeba5e241", "size_in_bytes": 4609}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "bef419eb9354d31e3f33733a49765abd410d0850c9e8f57acb1310616257777a", "sha256_in_prefix": "bef419eb9354d31e3f33733a49765abd410d0850c9e8f57acb1310616257777a", "size_in_bytes": 14998}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d805540a717aa41113697eba0ff58a2162be7253bd9f179741f5864eba5024ce", "sha256_in_prefix": "d805540a717aa41113697eba0ff58a2162be7253bd9f179741f5864eba5024ce", "size_in_bytes": 2568}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "path_type": "hardlink", "sha256": "a257e27d5eab31b48f1d20cdd60b4d53894ed7fbe2de9bad16358470fe528375", "sha256_in_prefix": "a257e27d5eab31b48f1d20cdd60b4d53894ed7fbe2de9bad16358470fe528375", "size_in_bytes": 40940}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c11dc1694292ac011a1b3eca04e3df2d74b1b57e43995173b758e5326846be7", "sha256_in_prefix": "8c11dc1694292ac011a1b3eca04e3df2d74b1b57e43995173b758e5326846be7", "size_in_bytes": 15752}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "47527681096df5e4439a3effdfb7926ad32c71671122132599e5e42df9fa37c2", "sha256_in_prefix": "47527681096df5e4439a3effdfb7926ad32c71671122132599e5e42df9fa37c2", "size_in_bytes": 1349}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "path_type": "hardlink", "sha256": "63d6c820914f0fa439590ce5db59f617f8a8989a432080b235e4778057442eef", "sha256_in_prefix": "63d6c820914f0fa439590ce5db59f617f8a8989a432080b235e4778057442eef", "size_in_bytes": 7369}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "path_type": "hardlink", "sha256": "4dd8a566ed021350afe71feee8e5c96fa1f7549179d02e0b8c8ecc079d4ef05c", "sha256_in_prefix": "4dd8a566ed021350afe71feee8e5c96fa1f7549179d02e0b8c8ecc079d4ef05c", "size_in_bytes": 9888}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "path_type": "hardlink", "sha256": "0732924eccf10e619fcd40219f96c2dd79eff4b383f20eddac53e183bc5b7a1a", "sha256_in_prefix": "0732924eccf10e619fcd40219f96c2dd79eff4b383f20eddac53e183bc5b7a1a", "size_in_bytes": 5854}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "path_type": "hardlink", "sha256": "29239b0b47fe3b764532b3f959ec66b9a04f42cbb6f58c41d6f76014e9d0ece4", "sha256_in_prefix": "29239b0b47fe3b764532b3f959ec66b9a04f42cbb6f58c41d6f76014e9d0ece4", "size_in_bytes": 10710}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "281c51335d2c2bbaa4ae3450bfa253c2b634796d8c508af49990a291c686436f", "sha256_in_prefix": "281c51335d2c2bbaa4ae3450bfa253c2b634796d8c508af49990a291c686436f", "size_in_bytes": 476}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "path_type": "hardlink", "sha256": "41bf5c42062ea3a80b5596a6ac67024856bf3785f52737813a41e5dc649cbff6", "sha256_in_prefix": "41bf5c42062ea3a80b5596a6ac67024856bf3785f52737813a41e5dc649cbff6", "size_in_bytes": 9049}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "71f1b77808f6b2f9318209f97e24117e2a5d24cfb37e691ac0c7e62c8dfd2f11", "sha256_in_prefix": "71f1b77808f6b2f9318209f97e24117e2a5d24cfb37e691ac0c7e62c8dfd2f11", "size_in_bytes": 313}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "3f157dcfbae02d7206e804070a497ff31533261d572150fa866895d31e7eaa21", "sha256_in_prefix": "3f157dcfbae02d7206e804070a497ff31533261d572150fa866895d31e7eaa21", "size_in_bytes": 17027}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "path_type": "hardlink", "sha256": "e2aa53db57bb22a6015560fc6c6328c39f5d57deef2567974b12c66a252baadb", "sha256_in_prefix": "e2aa53db57bb22a6015560fc6c6328c39f5d57deef2567974b12c66a252baadb", "size_in_bytes": 2859}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c486bc1ff32efc92cbe292cbca289fe88694f3c31a1fbfae3b075cf8323c3fd", "sha256_in_prefix": "7c486bc1ff32efc92cbe292cbca289fe88694f3c31a1fbfae3b075cf8323c3fd", "size_in_bytes": 283}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "c7d1356f3e7ac07e51cad7c08bd90b5e8aca1a03a798a87cddf3beeb7b26b6ae", "sha256_in_prefix": "c7d1356f3e7ac07e51cad7c08bd90b5e8aca1a03a798a87cddf3beeb7b26b6ae", "size_in_bytes": 1737}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-310.pyc", "path_type": "hardlink", "sha256": "3ca7adf10484c5c3fe1e586e98860d2d907cfa27412f3d59065c57dca8151603", "sha256_in_prefix": "3ca7adf10484c5c3fe1e586e98860d2d907cfa27412f3d59065c57dca8151603", "size_in_bytes": 19783}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-310.pyc", "path_type": "hardlink", "sha256": "286dd134a8496b464fe8ec83a2bf9fcdcf357478731ca86467989b4d9aa343a9", "sha256_in_prefix": "286dd134a8496b464fe8ec83a2bf9fcdcf357478731ca86467989b4d9aa343a9", "size_in_bytes": 3407}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-310.pyc", "path_type": "hardlink", "sha256": "55606c7846d8d3b2f0d065081a508ef2b16d3572354ad485fa3cbf53871948ce", "sha256_in_prefix": "55606c7846d8d3b2f0d065081a508ef2b16d3572354ad485fa3cbf53871948ce", "size_in_bytes": 7112}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-310.pyc", "path_type": "hardlink", "sha256": "4ef58db597ddb9718ef5764ab52b0c02487aa0651750d6cf27dcebef9c5d81b0", "sha256_in_prefix": "4ef58db597ddb9718ef5764ab52b0c02487aa0651750d6cf27dcebef9c5d81b0", "size_in_bytes": 2130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-310.pyc", "path_type": "hardlink", "sha256": "0cc7e220420195fdbab60849489ef6d206d02858baf6422026ce6d22b89b109a", "sha256_in_prefix": "0cc7e220420195fdbab60849489ef6d206d02858baf6422026ce6d22b89b109a", "size_in_bytes": 7673}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-310.pyc", "path_type": "hardlink", "sha256": "0cff1c2c9d090ab6a56140e14e982c8db6d76908173d52a93564017fb22b4d07", "sha256_in_prefix": "0cff1c2c9d090ab6a56140e14e982c8db6d76908173d52a93564017fb22b4d07", "size_in_bytes": 6975}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-310.pyc", "path_type": "hardlink", "sha256": "835137325f4542d91f009e9af5227b53c470af8480972bcf25ca358dfa767296", "sha256_in_prefix": "835137325f4542d91f009e9af5227b53c470af8480972bcf25ca358dfa767296", "size_in_bytes": 1599}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "4474f34b42d56756153bec53398f65f9c37df6c9c078ed3e2132f483e8492de3", "sha256_in_prefix": "4474f34b42d56756153bec53398f65f9c37df6c9c078ed3e2132f483e8492de3", "size_in_bytes": 4045}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-310.pyc", "path_type": "hardlink", "sha256": "bb56689e21e5417f53294fa63504fd69aa9299b5b7647eff4ebc0fa921cb3993", "sha256_in_prefix": "bb56689e21e5417f53294fa63504fd69aa9299b5b7647eff4ebc0fa921cb3993", "size_in_bytes": 2668}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "1876c75652fd5f6f3174bfe321f97fb7334025ebf558381775f949214145a6ac", "sha256_in_prefix": "1876c75652fd5f6f3174bfe321f97fb7334025ebf558381775f949214145a6ac", "size_in_bytes": 28110}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "c880a3ffff8563c9cb3d5996d384b25e4db4289bbd0377fbb05e59a153cefcb6", "sha256_in_prefix": "c880a3ffff8563c9cb3d5996d384b25e4db4289bbd0377fbb05e59a153cefcb6", "size_in_bytes": 1875}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "21cf10c8d0e2b78377ca15ccd18656142a1d3e44aade1bd66f95c0cd1f176864", "sha256_in_prefix": "21cf10c8d0e2b78377ca15ccd18656142a1d3e44aade1bd66f95c0cd1f176864", "size_in_bytes": 5156}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "59bac22b00a59d3e5608a56b8cf8efc43831a36b72792ee4389c9cd4669c7841", "sha256_in_prefix": "59bac22b00a59d3e5608a56b8cf8efc43831a36b72792ee4389c9cd4669c7841", "size_in_bytes": 2153}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "783e654742611af88cd9f00bf01a431a219db536556e63ff981c7bd673070ac9", "sha256_in_prefix": "783e654742611af88cd9f00bf01a431a219db536556e63ff981c7bd673070ac9", "size_in_bytes": 4557}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.43.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "0fa8e11f4d1e3336e0ad718078ec157c3e62fa508030cc9cb86d4bd2eb1e0e5a", "sha256_in_prefix": "0fa8e11f4d1e3336e0ad718078ec157c3e62fa508030cc9cb86d4bd2eb1e0e5a", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "df66ad8d5b5a44f603e6d8ea36c36703037114b74122ed09082a5f6213b3b6e7", "sha256_in_prefix": "df66ad8d5b5a44f603e6d8ea36c36703037114b74122ed09082a5f6213b3b6e7", "size_in_bytes": 220}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f5f8f8a1d2564f19fc58056f19c7551a19c4a4feb6b59427fb94aeacf48bf30b", "sha256_in_prefix": "f5f8f8a1d2564f19fc58056f19c7551a19c4a4feb6b59427fb94aeacf48bf30b", "size_in_bytes": 618}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "71a25723c21828ac75c834f736c603dde9fc933d614d442113e88061bb943108", "sha256_in_prefix": "71a25723c21828ac75c834f736c603dde9fc933d614d442113e88061bb943108", "size_in_bytes": 924}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "6b23a5e7e4365797ccdd6b967f5f4a24913b622f2eb2f1127d70eb11663a0c9a", "sha256_in_prefix": "6b23a5e7e4365797ccdd6b967f5f4a24913b622f2eb2f1127d70eb11663a0c9a", "size_in_bytes": 14192}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "ba89f05dee19e6ab4a803a5ce50785602c721bae1991955a696aa2104fddae6c", "sha256_in_prefix": "ba89f05dee19e6ab4a803a5ce50785602c721bae1991955a696aa2104fddae6c", "size_in_bytes": 9891}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "20886510c3fdb8e56ed35e9d33b44763452ce39d12b3ccbdedfe693b32b12822", "sha256_in_prefix": "20886510c3fdb8e56ed35e9d33b44763452ce39d12b3ccbdedfe693b32b12822", "size_in_bytes": 5813}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-310.pyc", "path_type": "hardlink", "sha256": "88ebc9e3e4e2532e6e941c221dc3edb08a9cea7c426079ef96626a2811c5539e", "sha256_in_prefix": "88ebc9e3e4e2532e6e941c221dc3edb08a9cea7c426079ef96626a2811c5539e", "size_in_bytes": 856}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "b9ea95b8c7648395055ab58008f30e4e49155fa9d22f75beebd62afbda154fa3", "sha256_in_prefix": "b9ea95b8c7648395055ab58008f30e4e49155fa9d22f75beebd62afbda154fa3", "size_in_bytes": 5625}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "3680a78c9e03144678e44a3ed817572ec5890b01a46a2b75b69ff5ee96a5795c", "sha256_in_prefix": "3680a78c9e03144678e44a3ed817572ec5890b01a46a2b75b69ff5ee96a5795c", "size_in_bytes": 746}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "38a272a7d13cdf5cc9af1a117e633d0203a30721b5081fa9cc5e645d016668a9", "sha256_in_prefix": "38a272a7d13cdf5cc9af1a117e633d0203a30721b5081fa9cc5e645d016668a9", "size_in_bytes": 20938}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "7813619cfc164ed74a0091f2efb96fcfb80e43912edc66af1ae817c614ac9fe5", "sha256_in_prefix": "7813619cfc164ed74a0091f2efb96fcfb80e43912edc66af1ae817c614ac9fe5", "size_in_bytes": 4264}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "905382d2d5ae05318e59ad86d55a78685d6b0ae6e176c09484acb8198775bc3d", "sha256_in_prefix": "905382d2d5ae05318e59ad86d55a78685d6b0ae6e176c09484acb8198775bc3d", "size_in_bytes": 4509}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-310.pyc", "path_type": "hardlink", "sha256": "844d5d869724111c7ec0af556342aa3a8e52bc4d20c69a52bcdf1c5dcfecd223", "sha256_in_prefix": "844d5d869724111c7ec0af556342aa3a8e52bc4d20c69a52bcdf1c5dcfecd223", "size_in_bytes": 6346}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-310.pyc", "path_type": "hardlink", "sha256": "ec4f44ce1772f21c8da8bc1cf1e66cf99948a9561dd93aa0353a48f30ca4614f", "sha256_in_prefix": "ec4f44ce1772f21c8da8bc1cf1e66cf99948a9561dd93aa0353a48f30ca4614f", "size_in_bytes": 3088}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "06ac14e1902a3fda72d5075ece4ecb37c1b6e0de93647a4eb25b73ccfc1e511b", "sha256_in_prefix": "06ac14e1902a3fda72d5075ece4ecb37c1b6e0de93647a4eb25b73ccfc1e511b", "size_in_bytes": 3837}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-310.pyc", "path_type": "hardlink", "sha256": "0d59bc2024b6cc70cc073ca272766f217d7335ef44ed41214a98ec7f72ef5e2b", "sha256_in_prefix": "0d59bc2024b6cc70cc073ca272766f217d7335ef44ed41214a98ec7f72ef5e2b", "size_in_bytes": 1090}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "a897296062aa75fc353fa05e9603751e7fecb8d80ce9bbf211616565eb925b1d", "sha256_in_prefix": "a897296062aa75fc353fa05e9603751e7fecb8d80ce9bbf211616565eb925b1d", "size_in_bytes": 9439}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "1e75ba38f74df7dde9b12b6fc25e3dc6dc76930ee1a156deea7bf099ff16b0a2", "sha256_in_prefix": "1e75ba38f74df7dde9b12b6fc25e3dc6dc76930ee1a156deea7bf099ff16b0a2", "size_in_bytes": 16103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "abec420aa4802bb1f3c99c4af40ebf1c05a686a4b5a01e01170d7eac74310624", "sha256_in_prefix": "abec420aa4802bb1f3c99c4af40ebf1c05a686a4b5a01e01170d7eac74310624", "size_in_bytes": 5884}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "sha256_in_prefix": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "size_in_bytes": 621}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b96a4d0237912c4443ed6f097642c6012c0d4951486ada060ac5a215c2b98a10", "sha256_in_prefix": "b96a4d0237912c4443ed6f097642c6012c0d4951486ada060ac5a215c2b98a10", "size_in_bytes": 156}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "dfa515c52dce7872094ac007b7a02aa98d7428f4b473b95aa7cf8042aa590d9b", "sha256_in_prefix": "dfa515c52dce7872094ac007b7a02aa98d7428f4b473b95aa7cf8042aa590d9b", "size_in_bytes": 166}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "hardlink", "sha256": "72e5a1f3ff47f16cac153cd3f823f85ca6ce59f104d21ab889327bd8375a5315", "sha256_in_prefix": "72e5a1f3ff47f16cac153cd3f823f85ca6ce59f104d21ab889327bd8375a5315", "size_in_bytes": 3288}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "afa9785e12e245f5336d714802785534bfbbac59bd3279802489b19a9920c6ab", "sha256_in_prefix": "afa9785e12e245f5336d714802785534bfbbac59bd3279802489b19a9920c6ab", "size_in_bytes": 6397}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "724f597b332e443915da9ce2dc7c6f95ef22c435ad693fe9d1f7102d33beb5f1", "sha256_in_prefix": "724f597b332e443915da9ce2dc7c6f95ef22c435ad693fe9d1f7102d33beb5f1", "size_in_bytes": 3316}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "308f321962b3571886dad0605905a5a95d6e8e85f008664fab158e88532e8248", "sha256_in_prefix": "308f321962b3571886dad0605905a5a95d6e8e85f008664fab158e88532e8248", "size_in_bytes": 8940}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "hardlink", "sha256": "87d801340bf1ebf9fcdcd5db1f3d3119a496b65e67b75aa3384d4588b2d42bb5", "sha256_in_prefix": "87d801340bf1ebf9fcdcd5db1f3d3119a496b65e67b75aa3384d4588b2d42bb5", "size_in_bytes": 2678}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "hardlink", "sha256": "3211ed9fd31d7ef21df4f388120e030daf7b4ba221d7da253efe97d82b8d0e6c", "sha256_in_prefix": "3211ed9fd31d7ef21df4f388120e030daf7b4ba221d7da253efe97d82b8d0e6c", "size_in_bytes": 5798}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "0dc0a0b8d47827ef5acd7e1407e9ba41542af6ca4359f5a79febc471389eb67a", "sha256_in_prefix": "0dc0a0b8d47827ef5acd7e1407e9ba41542af6ca4359f5a79febc471389eb67a", "size_in_bytes": 6884}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "hardlink", "sha256": "a7adb0e30b7d58097dae4432b26b75e1ebc3338fa806d296dd56b332f09c4936", "sha256_in_prefix": "a7adb0e30b7d58097dae4432b26b75e1ebc3338fa806d296dd56b332f09c4936", "size_in_bytes": 2811}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "hardlink", "sha256": "c9b397437ace53a7e3ea2870ed0af9776947e92ed7b0d0925910d370f54c375d", "sha256_in_prefix": "c9b397437ace53a7e3ea2870ed0af9776947e92ed7b0d0925910d370f54c375d", "size_in_bytes": 30970}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "ce747dc32e35e953ef6401080b0b89aec080f026c9c5b16d100b6cb20568f792", "sha256_in_prefix": "ce747dc32e35e953ef6401080b0b89aec080f026c9c5b16d100b6cb20568f792", "size_in_bytes": 13778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "eca687aa2dfd900e6b40e2139fede8742ea0409e08a3410c1a363e549b00f57a", "sha256_in_prefix": "eca687aa2dfd900e6b40e2139fede8742ea0409e08a3410c1a363e549b00f57a", "size_in_bytes": 4496}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "85f09afe3d1a49df2e68e4f4420605f94d46a033ebdd3d3c081f5db8726c9102", "sha256_in_prefix": "85f09afe3d1a49df2e68e4f4420605f94d46a033ebdd3d3c081f5db8726c9102", "size_in_bytes": 14141}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "0ed2435a864cbe7061e2578d3033c63a9ad053d77f769eaaf8c995d14fbee317", "sha256_in_prefix": "0ed2435a864cbe7061e2578d3033c63a9ad053d77f769eaaf8c995d14fbee317", "size_in_bytes": 7694}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "6c81f0eaa2baf09fd4a0adbe4fee35bc8fc8fb3e01375f35ed607abe70074860", "sha256_in_prefix": "6c81f0eaa2baf09fd4a0adbe4fee35bc8fc8fb3e01375f35ed607abe70074860", "size_in_bytes": 16164}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-310.pyc", "path_type": "hardlink", "sha256": "d7d5a70fadd7e6d9b3360200df3f2a8245b295050fefb9691a210095f697eee7", "sha256_in_prefix": "d7d5a70fadd7e6d9b3360200df3f2a8245b295050fefb9691a210095f697eee7", "size_in_bytes": 3926}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "79547148f3fd1f9affbf4037788d93f0953808c95a0fbd94233023e3547b4401", "sha256_in_prefix": "79547148f3fd1f9affbf4037788d93f0953808c95a0fbd94233023e3547b4401", "size_in_bytes": 153}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-310.pyc", "path_type": "hardlink", "sha256": "e53088229fdbfa501d4a4a595fd5e7d007da473924fe9acacd1d284c3331dc53", "sha256_in_prefix": "e53088229fdbfa501d4a4a595fd5e7d007da473924fe9acacd1d284c3331dc53", "size_in_bytes": 394}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "Lib/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "Lib/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "sha256_in_prefix": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "size_in_bytes": 20446}, {"_path": "Lib/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b6c82746535a058b22c9b89880c319cccb975ce28c50f756745b7ce3701357fe", "sha256_in_prefix": "b6c82746535a058b22c9b89880c319cccb975ce28c50f756745b7ce3701357fe", "size_in_bytes": 399}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-310.pyc", "path_type": "hardlink", "sha256": "1113bff6c690677f1d4bea36639879e35c909846054f5fe38752d82f1cf8fa78", "sha256_in_prefix": "1113bff6c690677f1d4bea36639879e35c909846054f5fe38752d82f1cf8fa78", "size_in_bytes": 4653}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-310.pyc", "path_type": "hardlink", "sha256": "cc4df127ed197138b317a579fe7f769c838c15d425a2a564e04cb4ff444f66e8", "sha256_in_prefix": "cc4df127ed197138b317a579fe7f769c838c15d425a2a564e04cb4ff444f66e8", "size_in_bytes": 2352}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-310.pyc", "path_type": "hardlink", "sha256": "8bfaa97a8aa0126d26fe4f9bb62954ecd797d8de3fc551837b161d22e72cd4b3", "sha256_in_prefix": "8bfaa97a8aa0126d26fe4f9bb62954ecd797d8de3fc551837b161d22e72cd4b3", "size_in_bytes": 13699}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-310.pyc", "path_type": "hardlink", "sha256": "87b8ff2de2689883f73f769312d6fa71df3f4e7817dddb15bfbe011b9dcb6949", "sha256_in_prefix": "87b8ff2de2689883f73f769312d6fa71df3f4e7817dddb15bfbe011b9dcb6949", "size_in_bytes": 1772}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "97efcf9f9328c7af481fb3080038bfd26ea8e9414f43c72a700ce0f95d158869", "sha256_in_prefix": "97efcf9f9328c7af481fb3080038bfd26ea8e9414f43c72a700ce0f95d158869", "size_in_bytes": 15470}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build.cpython-310.pyc", "path_type": "hardlink", "sha256": "8734f913fbebb72ee3ecb2a3b0bc7f27e473917d97b44ec45ebb6a40d7733546", "sha256_in_prefix": "8734f913fbebb72ee3ecb2a3b0bc7f27e473917d97b44ec45ebb6a40d7733546", "size_in_bytes": 5272}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "69e97e6bdc041e8041d96fe82ee4a79f79c5b358d813d6c856ed81f391d31ad3", "sha256_in_prefix": "69e97e6bdc041e8041d96fe82ee4a79f79c5b358d813d6c856ed81f391d31ad3", "size_in_bytes": 2488}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "97d68c11105236bdc439e69cf6eac49d59f6aa1167815d148cfbcb4405ffeab3", "sha256_in_prefix": "97d68c11105236bdc439e69cf6eac49d59f6aa1167815d148cfbcb4405ffeab3", "size_in_bytes": 14038}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "d8e003f8144f0f80400a763c600ea4b53d24afb64b97a22ee90046fe6172c132", "sha256_in_prefix": "d8e003f8144f0f80400a763c600ea4b53d24afb64b97a22ee90046fe6172c132", "size_in_bytes": 14959}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-310.pyc", "path_type": "hardlink", "sha256": "b58fbab96cc2ec26279f4d4e43d7f0233084ba4f73b5810cc2fa862451089bfa", "sha256_in_prefix": "b58fbab96cc2ec26279f4d4e43d7f0233084ba4f73b5810cc2fa862451089bfa", "size_in_bytes": 6084}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "1d658a71da9687ffcbb41f452b061f11364473970b76223e2f819959edbcef38", "sha256_in_prefix": "1d658a71da9687ffcbb41f452b061f11364473970b76223e2f819959edbcef38", "size_in_bytes": 3244}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "beef74887ad7ab8b0866e24ce8bca4a686ee6029b9877776d3aea52e1a9e0078", "sha256_in_prefix": "beef74887ad7ab8b0866e24ce8bca4a686ee6029b9877776d3aea52e1a9e0078", "size_in_bytes": 65181}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "4766176202e23e7f6173160bf068b12a834b9ff79c453ed56048d2b0a6cc9182", "sha256_in_prefix": "4766176202e23e7f6173160bf068b12a834b9ff79c453ed56048d2b0a6cc9182", "size_in_bytes": 35765}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "ac0701f200cb924f8a67c3dc6883547da93f34c4fe4eae0d1888d17425558fff", "sha256_in_prefix": "ac0701f200cb924f8a67c3dc6883547da93f34c4fe4eae0d1888d17425558fff", "size_in_bytes": 22261}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install.cpython-310.pyc", "path_type": "hardlink", "sha256": "edc2b46e043e15c654007ffb0ddb2cfea25428141ca57321753b7284ea7a2a06", "sha256_in_prefix": "edc2b46e043e15c654007ffb0ddb2cfea25428141ca57321753b7284ea7a2a06", "size_in_bytes": 5413}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "6b27c4c85e55859c99d50723848c6f729d470158f41a0a6c8c6725ea3befcc1a", "sha256_in_prefix": "6b27c4c85e55859c99d50723848c6f729d470158f41a0a6c8c6725ea3befcc1a", "size_in_bytes": 2362}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-310.pyc", "path_type": "hardlink", "sha256": "a2de8fe33d646894444c4fdf9ebd196ffeb09b6f1f8bff66c495d4fbc44991c3", "sha256_in_prefix": "a2de8fe33d646894444c4fdf9ebd196ffeb09b6f1f8bff66c495d4fbc44991c3", "size_in_bytes": 4504}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "735c0a641c014df4eb80c2e3836cd43b58a4ed1f27382c409f1c1bc18e5643e6", "sha256_in_prefix": "735c0a641c014df4eb80c2e3836cd43b58a4ed1f27382c409f1c1bc18e5643e6", "size_in_bytes": 2571}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-310.pyc", "path_type": "hardlink", "sha256": "301e4705b2c75009e59ecb241e5da6b0770d4fc8823854b1d9690380f1a277ae", "sha256_in_prefix": "301e4705b2c75009e59ecb241e5da6b0770d4fc8823854b1d9690380f1a277ae", "size_in_bytes": 2618}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-310.pyc", "path_type": "hardlink", "sha256": "34f05c2385c49dc9a0dc19131be8f1a4b0aef634d696b16089efd5679e9d22db", "sha256_in_prefix": "34f05c2385c49dc9a0dc19131be8f1a4b0aef634d696b16089efd5679e9d22db", "size_in_bytes": 900}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "5cc12bafb6b1796780df48abe6362dd997809ba02ac806d7e4f094af94f8b03c", "sha256_in_prefix": "5cc12bafb6b1796780df48abe6362dd997809ba02ac806d7e4f094af94f8b03c", "size_in_bytes": 7971}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-310.pyc", "path_type": "hardlink", "sha256": "56f5686a7adcb4c710c09a927cc48a2a62f433509252d8a6207c31a3bfd7a206", "sha256_in_prefix": "56f5686a7adcb4c710c09a927cc48a2a62f433509252d8a6207c31a3bfd7a206", "size_in_bytes": 4742}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/test.cpython-310.pyc", "path_type": "hardlink", "sha256": "49442e4fde063b05e584588cbc0e9276efc02a915adfb5639a158b1b6cdf532c", "sha256_in_prefix": "49442e4fde063b05e584588cbc0e9276efc02a915adfb5639a158b1b6cdf532c", "size_in_bytes": 1702}, {"_path": "Lib/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "Lib/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "Lib/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "sha256_in_prefix": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "size_in_bytes": 16972}, {"_path": "Lib/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "Lib/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "402b649748922e133917ae6100285afad006c4e771bbd76d24d0612826b8c0b2", "sha256_in_prefix": "402b649748922e133917ae6100285afad006c4e771bbd76d24d0612826b8c0b2", "size_in_bytes": 22274}, {"_path": "Lib/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "Lib/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "Lib/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "sha256_in_prefix": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "size_in_bytes": 18377}, {"_path": "Lib/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "Lib/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "sha256_in_prefix": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "size_in_bytes": 6886}, {"_path": "Lib/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "Lib/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "sha256_in_prefix": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "size_in_bytes": 87870}, {"_path": "Lib/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "11fa32d549d3f863bb203d417681cd75e1f3364bce5d8adc8804df11f6322dd1", "sha256_in_prefix": "11fa32d549d3f863bb203d417681cd75e1f3364bce5d8adc8804df11f6322dd1", "size_in_bytes": 35626}, {"_path": "Lib/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "sha256_in_prefix": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "size_in_bytes": 25982}, {"_path": "Lib/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "d4f29fa04e05f5f4c31e671c7e50e791bdbcf4221c95d63768fb47c98baeb073", "sha256_in_prefix": "d4f29fa04e05f5f4c31e671c7e50e791bdbcf4221c95d63768fb47c98baeb073", "size_in_bytes": 7045}, {"_path": "Lib/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "Lib/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "Lib/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "sha256_in_prefix": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "size_in_bytes": 2637}, {"_path": "Lib/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "Lib/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "Lib/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "Lib/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "sha256_in_prefix": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "size_in_bytes": 7374}, {"_path": "Lib/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "Lib/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "Lib/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d6bb7e679e88b455b6f5f0e81f6b0d3f0c040820c0b902556346d68d949611b2", "sha256_in_prefix": "d6bb7e679e88b455b6f5f0e81f6b0d3f0c040820c0b902556346d68d949611b2", "size_in_bytes": 140}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-310.pyc", "path_type": "hardlink", "sha256": "0e79e599da08e93074cd927dd17f0edee74e94fa913f540d604683c31792b4ec", "sha256_in_prefix": "0e79e599da08e93074cd927dd17f0edee74e94fa913f540d604683c31792b4ec", "size_in_bytes": 252}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-310.pyc", "path_type": "hardlink", "sha256": "347a53eaed2f5f360cc8a63788b69335e22bda390020e537967ed9f0d5abd8ae", "sha256_in_prefix": "347a53eaed2f5f360cc8a63788b69335e22bda390020e537967ed9f0d5abd8ae", "size_in_bytes": 1164}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-310.pyc", "path_type": "hardlink", "sha256": "6810e241b17ccc7cc9b587902ec7cc8b36217d9c50df7e19d59deae2950ebdaf", "sha256_in_prefix": "6810e241b17ccc7cc9b587902ec7cc8b36217d9c50df7e19d59deae2950ebdaf", "size_in_bytes": 371}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "a0b32d760e4d64d7590f4a64c0b33b3a11e895577838182afba61bc6d4deb612", "sha256_in_prefix": "a0b32d760e4d64d7590f4a64c0b33b3a11e895577838182afba61bc6d4deb612", "size_in_bytes": 226}, {"_path": "Lib/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "Lib/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "Lib/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "Lib/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "c8c4b1c064bce79176f6a0e623d62a502fae328a5bbf06ff054c73fc60812884", "sha256_in_prefix": "c8c4b1c064bce79176f6a0e623d62a502fae328a5bbf06ff054c73fc60812884", "size_in_bytes": 1599}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "6a58fda6866ae607a1890c796d75442742af37e1737f8027bdcd6d7ff202a68b", "sha256_in_prefix": "6a58fda6866ae607a1890c796d75442742af37e1737f8027bdcd6d7ff202a68b", "size_in_bytes": 16851}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-310.pyc", "path_type": "hardlink", "sha256": "466d6ddb31bad9c4e312c2ad596e665768125403416a904f51ac8bac741f8aec", "sha256_in_prefix": "466d6ddb31bad9c4e312c2ad596e665768125403416a904f51ac8bac741f8aec", "size_in_bytes": 18203}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "6489795deed8c978d11fc6c8369eed50d9262ee041b14489989547aedb3f23c7", "sha256_in_prefix": "6489795deed8c978d11fc6c8369eed50d9262ee041b14489989547aedb3f23c7", "size_in_bytes": 15988}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-310.pyc", "path_type": "hardlink", "sha256": "09e45f09aea3e203b58ca3d7100b78fe1c0dbef432ab0001b0122245d31624a8", "sha256_in_prefix": "09e45f09aea3e203b58ca3d7100b78fe1c0dbef432ab0001b0122245d31624a8", "size_in_bytes": 24163}, {"_path": "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "3d3798419b484b9091330d8bd65c2737b8e9b4991c090663b18aade27fc7d37a", "sha256_in_prefix": "3d3798419b484b9091330d8bd65c2737b8e9b4991c090663b18aade27fc7d37a", "size_in_bytes": 16988}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "09c9bcea95ca086f8bc5bed174e40bc835b297d40fb5f86bbbb570fe0a5581a7", "sha256_in_prefix": "09c9bcea95ca086f8bc5bed174e40bc835b297d40fb5f86bbbb570fe0a5581a7", "size_in_bytes": 18737}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "303e487ffb21327e74f140ecd5bd2b178a802248169f99c2f6f67dfe9f3bed6d", "sha256_in_prefix": "303e487ffb21327e74f140ecd5bd2b178a802248169f99c2f6f67dfe9f3bed6d", "size_in_bytes": 1479}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-310.pyc", "path_type": "hardlink", "sha256": "87dbb7c712addf52ad0c197f74dc98a93d91dfc68dbbc6d8c4fbbb6858c441ef", "sha256_in_prefix": "87dbb7c712addf52ad0c197f74dc98a93d91dfc68dbbc6d8c4fbbb6858c441ef", "size_in_bytes": 11999}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-310.pyc", "path_type": "hardlink", "sha256": "95a9f639ae3f825fa02e9bfa2c4bb0fdc8926d5f4a97eb53a7402439352930d5", "sha256_in_prefix": "95a9f639ae3f825fa02e9bfa2c4bb0fdc8926d5f4a97eb53a7402439352930d5", "size_in_bytes": 1573}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-310.pyc", "path_type": "hardlink", "sha256": "54a6f01f2e40f20566c3f6e0b139772caf71074ca16f96e1c9482ed26be7692a", "sha256_in_prefix": "54a6f01f2e40f20566c3f6e0b139772caf71074ca16f96e1c9482ed26be7692a", "size_in_bytes": 2405}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-310.pyc", "path_type": "hardlink", "sha256": "8f7d1601e498ffb2faeea512a568c63e72f8dd85fc1968e901814423eba2b004", "sha256_in_prefix": "8f7d1601e498ffb2faeea512a568c63e72f8dd85fc1968e901814423eba2b004", "size_in_bytes": 85753}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-310.pyc", "path_type": "hardlink", "sha256": "0ec2819c3bae571e17dcd93a61a6c83c8130400c3992f9ea2436a48ce3b6da5d", "sha256_in_prefix": "0ec2819c3bae571e17dcd93a61a6c83c8130400c3992f9ea2436a48ce3b6da5d", "size_in_bytes": 12490}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "91dd12598aeca7721717d28600cf10a5e68aa46c8cb0d80bfad8e1f533df8726", "sha256_in_prefix": "91dd12598aeca7721717d28600cf10a5e68aa46c8cb0d80bfad8e1f533df8726", "size_in_bytes": 1625}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "4fc46f2528b9dd805f0c1e2644fb85ea0b0fc71f3ebbc2985f9eb2deaa24a7fe", "sha256_in_prefix": "4fc46f2528b9dd805f0c1e2644fb85ea0b0fc71f3ebbc2985f9eb2deaa24a7fe", "size_in_bytes": 335460}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "323cfa463c7504c0f0d974cc01f4beb0ce71e45bf9697d9993fab933feeb7ff7", "sha256_in_prefix": "323cfa463c7504c0f0d974cc01f4beb0ce71e45bf9697d9993fab933feeb7ff7", "size_in_bytes": 12814}, {"_path": "Lib/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "Lib/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "Lib/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "Lib/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "Lib/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "Lib/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "Lib/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "f61cdee6922879848f14eaaf873d218adb9ac443ec578c0df6c63785c7f785f8", "sha256_in_prefix": "f61cdee6922879848f14eaaf873d218adb9ac443ec578c0df6c63785c7f785f8", "size_in_bytes": 38815}, {"_path": "Lib/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "Lib/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "Lib/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "Lib/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "sha256_in_prefix": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "size_in_bytes": 5110}, {"_path": "Lib/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "Lib/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "Lib/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "b7777f1d9ea032acaa9e1cf974f441b8ccb04d33e4dcbca5216933b744425673", "sha256_in_prefix": "b7777f1d9ea032acaa9e1cf974f441b8ccb04d33e4dcbca5216933b744425673", "size_in_bytes": 41255}, {"_path": "Lib/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "Lib/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "5c125b84d2017bb097808f73ce17b12826890cf5a28253bd9e81c4dbf8b0a689", "sha256_in_prefix": "5c125b84d2017bb097808f73ce17b12826890cf5a28253bd9e81c4dbf8b0a689", "size_in_bytes": 39315}, {"_path": "Lib/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "sha256_in_prefix": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "size_in_bytes": 14906}, {"_path": "Lib/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f50dba9fd17b63a60cca62748e08008bc67c4d6db400e78fff76876c1ffb140c", "sha256_in_prefix": "f50dba9fd17b63a60cca62748e08008bc67c4d6db400e78fff76876c1ffb140c", "size_in_bytes": 446}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-310.pyc", "path_type": "hardlink", "sha256": "4848d8608633b9c6f259d2a906db394c3167b2e8cfd54088aa3b8326aa0f91c7", "sha256_in_prefix": "4848d8608633b9c6f259d2a906db394c3167b2e8cfd54088aa3b8326aa0f91c7", "size_in_bytes": 3918}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-310.pyc", "path_type": "hardlink", "sha256": "b07e6341a718159e55b679d815cb9d010e5da76f3be80b895b8d20223d2fffa7", "sha256_in_prefix": "b07e6341a718159e55b679d815cb9d010e5da76f3be80b895b8d20223d2fffa7", "size_in_bytes": 2083}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-310.pyc", "path_type": "hardlink", "sha256": "8666e0c8fadbb4616eace9c17f175a5b55cb37d3ef4784a173d620c9c13f0905", "sha256_in_prefix": "8666e0c8fadbb4616eace9c17f175a5b55cb37d3ef4784a173d620c9c13f0905", "size_in_bytes": 4101}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-310.pyc", "path_type": "hardlink", "sha256": "7dacc4f1c06c68ff5df6c44d173ac2d965259c5f192b55f4a620baf2baffa419", "sha256_in_prefix": "7dacc4f1c06c68ff5df6c44d173ac2d965259c5f192b55f4a620baf2baffa419", "size_in_bytes": 169}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-310.pyc", "path_type": "hardlink", "sha256": "3b2a697a72dc18df5ba13a9c4f4fea0164a02a143fdc8e9d3311e4febdc43acd", "sha256_in_prefix": "3b2a697a72dc18df5ba13a9c4f4fea0164a02a143fdc8e9d3311e4febdc43acd", "size_in_bytes": 2719}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-310.pyc", "path_type": "hardlink", "sha256": "ab00cbca46508bfba66a7b34b1c428a31ade55758d69708ec25b47a9b6547212", "sha256_in_prefix": "ab00cbca46508bfba66a7b34b1c428a31ade55758d69708ec25b47a9b6547212", "size_in_bytes": 163}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-310.pyc", "path_type": "hardlink", "sha256": "2f2699726164b0ead098951b50d109519bfee1a48b4defd17b612126b959aed2", "sha256_in_prefix": "2f2699726164b0ead098951b50d109519bfee1a48b4defd17b612126b959aed2", "size_in_bytes": 3369}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "e5a8ad721681cbc7c89d6c3c37211a048c86ae44afd4238b3075e9b78341496f", "sha256_in_prefix": "e5a8ad721681cbc7c89d6c3c37211a048c86ae44afd4238b3075e9b78341496f", "size_in_bytes": 1137}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-310.pyc", "path_type": "hardlink", "sha256": "e5348434392b1652487926a91654cc9afb66f9d7c6ff8249394f412a8b8032f4", "sha256_in_prefix": "e5348434392b1652487926a91654cc9afb66f9d7c6ff8249394f412a8b8032f4", "size_in_bytes": 1014}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-310.pyc", "path_type": "hardlink", "sha256": "2efed365ebad0897ccb6ffefcd856fa0c73cf38e60ab0a3ed6aef19c4f1a4434", "sha256_in_prefix": "2efed365ebad0897ccb6ffefcd856fa0c73cf38e60ab0a3ed6aef19c4f1a4434", "size_in_bytes": 2348}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "10bfc0c3aeddd4b5e36259d00ad37f580039339581caa010ee231b262d5af2a2", "sha256_in_prefix": "10bfc0c3aeddd4b5e36259d00ad37f580039339581caa010ee231b262d5af2a2", "size_in_bytes": 20300}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c8e59b9622677bffa4d104b8857e7ea308faec9990cae9d8a20669d9c94a4d0", "sha256_in_prefix": "7c8e59b9622677bffa4d104b8857e7ea308faec9990cae9d8a20669d9c94a4d0", "size_in_bytes": 1371}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "7d2633f0925c35ee445a8c9b498b0ecb90083ffb25c6a8f2c331004b0f6fe8df", "sha256_in_prefix": "7d2633f0925c35ee445a8c9b498b0ecb90083ffb25c6a8f2c331004b0f6fe8df", "size_in_bytes": 2244}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "e4d34d967fa2a15c9d18959b2a6739f8cab6edb5866d8d9b19381c247d5cc5c3", "sha256_in_prefix": "e4d34d967fa2a15c9d18959b2a6739f8cab6edb5866d8d9b19381c247d5cc5c3", "size_in_bytes": 9356}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "125b2d43cdf2850c0c973cdaff9fe269fd0b5ad2144c2629343d62698cbe8e31", "sha256_in_prefix": "125b2d43cdf2850c0c973cdaff9fe269fd0b5ad2144c2629343d62698cbe8e31", "size_in_bytes": 28753}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "a5d64cfc3d0e5e6524c406599e5ff1c672b4bffdb823e6ce5913e35e77cef161", "sha256_in_prefix": "a5d64cfc3d0e5e6524c406599e5ff1c672b4bffdb823e6ce5913e35e77cef161", "size_in_bytes": 10830}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-310.pyc", "path_type": "hardlink", "sha256": "dae8476bd0d452695a65912f942b62256690cc132f5853ea2607c790c71854bc", "sha256_in_prefix": "dae8476bd0d452695a65912f942b62256690cc132f5853ea2607c790c71854bc", "size_in_bytes": 20514}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "4e3c50553a48928312807d82495e7344a6163cd6b2b4da4efc24ddbef3c39471", "sha256_in_prefix": "4e3c50553a48928312807d82495e7344a6163cd6b2b4da4efc24ddbef3c39471", "size_in_bytes": 14853}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c2e6323cdcfa413cd6e93002bb59370aa3c71777876a8f1989fd1e9e4066624", "sha256_in_prefix": "7c2e6323cdcfa413cd6e93002bb59370aa3c71777876a8f1989fd1e9e4066624", "size_in_bytes": 748}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-310.pyc", "path_type": "hardlink", "sha256": "8b2bc7e2339a135475d1018c0baab5cb3afc3cec1d45f880d001df348e3626d6", "sha256_in_prefix": "8b2bc7e2339a135475d1018c0baab5cb3afc3cec1d45f880d001df348e3626d6", "size_in_bytes": 5679}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "9384adb1b08ce061b32b8ac749dda42dbd67500f80cdf4c148a4279e04c84155", "sha256_in_prefix": "9384adb1b08ce061b32b8ac749dda42dbd67500f80cdf4c148a4279e04c84155", "size_in_bytes": 6917}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "f18ab0a883328a54285d6ae0eebf7f27f5cfd81938950049bf8e4e16b6485b83", "sha256_in_prefix": "f18ab0a883328a54285d6ae0eebf7f27f5cfd81938950049bf8e4e16b6485b83", "size_in_bytes": 7025}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-310.pyc", "path_type": "hardlink", "sha256": "9c302aa947b029f9d6a6ee7543f2e3aa1120d9470a2b9c99de9fc4786cbeec7c", "sha256_in_prefix": "9c302aa947b029f9d6a6ee7543f2e3aa1120d9470a2b9c99de9fc4786cbeec7c", "size_in_bytes": 5605}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "67536219dae52bb5b9e7309313312520982fa202d8db1ed16540ba43bca632eb", "sha256_in_prefix": "67536219dae52bb5b9e7309313312520982fa202d8db1ed16540ba43bca632eb", "size_in_bytes": 44432}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "5b22d9001efaf9f6044851edd1105d784c0bc1894aa71c907f6c8b313effe001", "sha256_in_prefix": "5b22d9001efaf9f6044851edd1105d784c0bc1894aa71c907f6c8b313effe001", "size_in_bytes": 36915}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "dc7eb255e1ebcc736ca20fddcb13d72d0af7fa8d2a29a611673dd89703c7be17", "sha256_in_prefix": "dc7eb255e1ebcc736ca20fddcb13d72d0af7fa8d2a29a611673dd89703c7be17", "size_in_bytes": 30660}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-310.pyc", "path_type": "hardlink", "sha256": "c365bb8b389fad3a0cee3f8b0865b5cb5a48c25eeb766798a6205033a2291475", "sha256_in_prefix": "c365bb8b389fad3a0cee3f8b0865b5cb5a48c25eeb766798a6205033a2291475", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-310.pyc", "path_type": "hardlink", "sha256": "1e59b3d682de9495cf786fe8dff47306e8720a58eb48581d7607e8df90b3e7b2", "sha256_in_prefix": "1e59b3d682de9495cf786fe8dff47306e8720a58eb48581d7607e8df90b3e7b2", "size_in_bytes": 8126}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-310.pyc", "path_type": "hardlink", "sha256": "5470510bbd7b89aa5e816f6d0251cc1abcc7e1407ba90ab85e65216b7181946e", "sha256_in_prefix": "5470510bbd7b89aa5e816f6d0251cc1abcc7e1407ba90ab85e65216b7181946e", "size_in_bytes": 2663}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-310.pyc", "path_type": "hardlink", "sha256": "4206d51aaadfae88d23450f350a1514dc019a882b87696357e9bf3acdeebb314", "sha256_in_prefix": "4206d51aaadfae88d23450f350a1514dc019a882b87696357e9bf3acdeebb314", "size_in_bytes": 953}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "ac39f9f0916fdebb68bb3d8bb517c7d8e8337ffabf4fc1e5111bee116c5f86c5", "sha256_in_prefix": "ac39f9f0916fdebb68bb3d8bb517c7d8e8337ffabf4fc1e5111bee116c5f86c5", "size_in_bytes": 3647}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "392ae4dd91566a20d90c4c009ce96420b8a8d17ac3ecda9cb134544afcb48329", "sha256_in_prefix": "392ae4dd91566a20d90c4c009ce96420b8a8d17ac3ecda9cb134544afcb48329", "size_in_bytes": 2025}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-310.pyc", "path_type": "hardlink", "sha256": "450857a38804d63e4698524a41b38a17d0a8e6119614fc33a9b4bf8c915bfd7c", "sha256_in_prefix": "450857a38804d63e4698524a41b38a17d0a8e6119614fc33a9b4bf8c915bfd7c", "size_in_bytes": 15914}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-310.pyc", "path_type": "hardlink", "sha256": "f4d163e6a24d63a86c19b97538b9bceaae639583bafcf424033197a25237139b", "sha256_in_prefix": "f4d163e6a24d63a86c19b97538b9bceaae639583bafcf424033197a25237139b", "size_in_bytes": 3467}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-310.pyc", "path_type": "hardlink", "sha256": "23e2e09f63b51aa54757b33e24b93867ce760c2e09f8c606d2d3042fe5f1d6fa", "sha256_in_prefix": "23e2e09f63b51aa54757b33e24b93867ce760c2e09f8c606d2d3042fe5f1d6fa", "size_in_bytes": 13922}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-310.pyc", "path_type": "hardlink", "sha256": "9645b5066a403acb651d981986c249c53595986e8ede1dff7845b077ab6fd783", "sha256_in_prefix": "9645b5066a403acb651d981986c249c53595986e8ede1dff7845b077ab6fd783", "size_in_bytes": 6238}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "c3d787eb0cf30b0dadd52121f6b723c1a17a91d316affd45e77ac6926132773a", "sha256_in_prefix": "c3d787eb0cf30b0dadd52121f6b723c1a17a91d316affd45e77ac6926132773a", "size_in_bytes": 28178}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-310.pyc", "path_type": "hardlink", "sha256": "73030626cfb1725317907205d4243c8fa9f69ceeaa1ae7241fe6f7b1b78cae09", "sha256_in_prefix": "73030626cfb1725317907205d4243c8fa9f69ceeaa1ae7241fe6f7b1b78cae09", "size_in_bytes": 1887}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-310.pyc", "path_type": "hardlink", "sha256": "530f63321af56b57d1310925fabe9a999cd5724e1d780ded1e39df5c873f64bd", "sha256_in_prefix": "530f63321af56b57d1310925fabe9a999cd5724e1d780ded1e39df5c873f64bd", "size_in_bytes": 9914}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-310.pyc", "path_type": "hardlink", "sha256": "e44aa1d697fa2819cf5854e361d04ffdb70fe5c06fedeaff9e0bbb197dbbe61c", "sha256_in_prefix": "e44aa1d697fa2819cf5854e361d04ffdb70fe5c06fedeaff9e0bbb197dbbe61c", "size_in_bytes": 819}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "f68e509b64dad1c195726416bc9bf1f7bdbc36356366edfe4b36609b434bf959", "sha256_in_prefix": "f68e509b64dad1c195726416bc9bf1f7bdbc36356366edfe4b36609b434bf959", "size_in_bytes": 675}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-310.pyc", "path_type": "hardlink", "sha256": "4b33efac66381d43a45bfca29b16be7c884bd57be5cfb8669e029a649fd4e4e6", "sha256_in_prefix": "4b33efac66381d43a45bfca29b16be7c884bd57be5cfb8669e029a649fd4e4e6", "size_in_bytes": 2765}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-310.pyc", "path_type": "hardlink", "sha256": "a65d66a03f66b3822790a3eb7a77d3c06890b677b02765b0367c525931b854f6", "sha256_in_prefix": "a65d66a03f66b3822790a3eb7a77d3c06890b677b02765b0367c525931b854f6", "size_in_bytes": 3190}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "41d2bbcdd9140416e1176b78d2c48f568c52695cd65eb8248ef5179801fb7842", "sha256_in_prefix": "41d2bbcdd9140416e1176b78d2c48f568c52695cd65eb8248ef5179801fb7842", "size_in_bytes": 13199}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-310.pyc", "path_type": "hardlink", "sha256": "67759f544c713dcfa6460b607528aad5ae943da25270864019048e36c0a141cc", "sha256_in_prefix": "67759f544c713dcfa6460b607528aad5ae943da25270864019048e36c0a141cc", "size_in_bytes": 7403}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-310.pyc", "path_type": "hardlink", "sha256": "716177776e04c02acf4f2ae99df69c37f581c98b2b7c5c0964cfb1c11a872a1d", "sha256_in_prefix": "716177776e04c02acf4f2ae99df69c37f581c98b2b7c5c0964cfb1c11a872a1d", "size_in_bytes": 383}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-310.pyc", "path_type": "hardlink", "sha256": "1d177830e22bc696d05a3d52367f6c34df99e8b457597c2ed25e255e372f31c7", "sha256_in_prefix": "1d177830e22bc696d05a3d52367f6c34df99e8b457597c2ed25e255e372f31c7", "size_in_bytes": 308}, {"_path": "Lib/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "cb489d6b02aa518f4bcf12c3bcd42ce9a7d68cb7676c849a54dd2e4258131d47", "sha256_in_prefix": "cb489d6b02aa518f4bcf12c3bcd42ce9a7d68cb7676c849a54dd2e4258131d47", "size_in_bytes": 146}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "d9b98596586bfbb4357b7935354c9b381ff6ef2b118caa68612797422a471b0f", "sha256_in_prefix": "d9b98596586bfbb4357b7935354c9b381ff6ef2b118caa68612797422a471b0f", "size_in_bytes": 271}, {"_path": "Lib/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "Lib/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "52efde56f6e8c6376e7e51f91788fca82209438f6aaf0fe3d400299f1759575b", "sha256_in_prefix": "52efde56f6e8c6376e7e51f91788fca82209438f6aaf0fe3d400299f1759575b", "size_in_bytes": 146}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "988c810ddc39e66c5599077b2aa312ac3f3bfefd12b6873ae1419717b5432c03", "sha256_in_prefix": "988c810ddc39e66c5599077b2aa312ac3f3bfefd12b6873ae1419717b5432c03", "size_in_bytes": 19074}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-310.pyc", "path_type": "hardlink", "sha256": "b38a8d7e61a56b071dd0d6837f59cab755c3b0e00741fb24630a55d060b1f914", "sha256_in_prefix": "b38a8d7e61a56b071dd0d6837f59cab755c3b0e00741fb24630a55d060b1f914", "size_in_bytes": 7774}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "e915c9b8a570dc1a352c15110e9e8fe72355e414b84221195a5efbe6ade89ce1", "sha256_in_prefix": "e915c9b8a570dc1a352c15110e9e8fe72355e414b84221195a5efbe6ade89ce1", "size_in_bytes": 11062}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-310.pyc", "path_type": "hardlink", "sha256": "548f97cd1eeaf9310e32439a90d95e516f39bf1b02308835579021a942ef3b02", "sha256_in_prefix": "548f97cd1eeaf9310e32439a90d95e516f39bf1b02308835579021a942ef3b02", "size_in_bytes": 3360}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-310.pyc", "path_type": "hardlink", "sha256": "669f19fef6713290900cfeebccd35d28dc5dc0e426ceb9570b2d2b39c7445323", "sha256_in_prefix": "669f19fef6713290900cfeebccd35d28dc5dc0e426ceb9570b2d2b39c7445323", "size_in_bytes": 27914}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "135fd4dd979ced3670eee6bfa5f1306c43bd71cecf7ac6289235fff7dda9bc28", "sha256_in_prefix": "135fd4dd979ced3670eee6bfa5f1306c43bd71cecf7ac6289235fff7dda9bc28", "size_in_bytes": 2136}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-310.pyc", "path_type": "hardlink", "sha256": "fe6c3240b16dfcf7468c9635cf54aa406bd8d2b07ad16b1aed085aa0bd82bf6b", "sha256_in_prefix": "fe6c3240b16dfcf7468c9635cf54aa406bd8d2b07ad16b1aed085aa0bd82bf6b", "size_in_bytes": 639}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "5637e79abdbfec666609c063f2afab53df60836786ac93dba898eafdd4add6e5", "sha256_in_prefix": "5637e79abdbfec666609c063f2afab53df60836786ac93dba898eafdd4add6e5", "size_in_bytes": 20286}, {"_path": "Lib/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "af965027a9adea6c9b6f175fbfcbba3e5e0ae4fb453623545b1728862c33f00c", "sha256_in_prefix": "af965027a9adea6c9b6f175fbfcbba3e5e0ae4fb453623545b1728862c33f00c", "size_in_bytes": 33189}, {"_path": "Lib/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "Lib/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "Lib/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "Lib/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "8cfd566769468c3dcb641a9e41d4abf988871717bb2ab732282d3f060023e977", "sha256_in_prefix": "8cfd566769468c3dcb641a9e41d4abf988871717bb2ab732282d3f060023e977", "size_in_bytes": 151}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-310.pyc", "path_type": "hardlink", "sha256": "033b57f696b7d25755f01cea4a7204b4cd70326547a0117c5917e536f2dfe8d5", "sha256_in_prefix": "033b57f696b7d25755f01cea4a7204b4cd70326547a0117c5917e536f2dfe8d5", "size_in_bytes": 3169}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "895bebae9c21535829abee9dd2b24af7c38dcdf0056f704eb03296ae4696977f", "sha256_in_prefix": "895bebae9c21535829abee9dd2b24af7c38dcdf0056f704eb03296ae4696977f", "size_in_bytes": 6089}, {"_path": "Lib/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "b995b4dded78d69fbe035e8eb78600ba0052de1eb20211253da4324d22409f32", "sha256_in_prefix": "b995b4dded78d69fbe035e8eb78600ba0052de1eb20211253da4324d22409f32", "size_in_bytes": 8204}, {"_path": "Lib/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "Lib/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "Lib/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "sha256_in_prefix": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "size_in_bytes": 2397}, {"_path": "Lib/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "0b73e9ee9d9f5021afb87d0876af25082d80061b2ee06da237925aa1cc8d1d0c", "sha256_in_prefix": "0b73e9ee9d9f5021afb87d0876af25082d80061b2ee06da237925aa1cc8d1d0c", "size_in_bytes": 19906}, {"_path": "Lib/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "Lib/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "Lib/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "Lib/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "891a0639fe6f7a196d4e575b82173dee91e3fbc9c3c9afcd97df81b1dca5f10d", "sha256_in_prefix": "891a0639fe6f7a196d4e575b82173dee91e3fbc9c3c9afcd97df81b1dca5f10d", "size_in_bytes": 33574}, {"_path": "Lib/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "fb50e71042606af4053f3a0b80773e42b885bf496568ea75604afb2c35c2481a", "sha256_in_prefix": "fb50e71042606af4053f3a0b80773e42b885bf496568ea75604afb2c35c2481a", "size_in_bytes": 14187}, {"_path": "Lib/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "Lib/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "1699cf8ecb919c0fbc8d10ef05eda823fdef8afc233533ee1760acc0238d5c0e", "sha256_in_prefix": "1699cf8ecb919c0fbc8d10ef05eda823fdef8afc233533ee1760acc0238d5c0e", "size_in_bytes": 19102}, {"_path": "Lib/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "Lib/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "Lib/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "1db4d9a7b0f93732c08776edad4f0b7ae66fe127c00036bd115b9b799caede21", "sha256_in_prefix": "1db4d9a7b0f93732c08776edad4f0b7ae66fe127c00036bd115b9b799caede21", "size_in_bytes": 8897}, {"_path": "Lib/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "020cb72f4337845e5dc57c12a4b27021b809237383cbf6ec1f6ddc1144763a53", "sha256_in_prefix": "020cb72f4337845e5dc57c12a4b27021b809237383cbf6ec1f6ddc1144763a53", "size_in_bytes": 7094}, {"_path": "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "Lib/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "0fc86c93e67485872b2a8c8a33c027291d087557e05c3951db20114a85c3cd37", "sha256_in_prefix": "0fc86c93e67485872b2a8c8a33c027291d087557e05c3951db20114a85c3cd37", "size_in_bytes": 53308}, {"_path": "Lib/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "sha256_in_prefix": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "size_in_bytes": 43383}, {"_path": "Lib/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "d018b6f489be3f3fd2bcab4a564abbb32dad60476bbdfbc23b27b17d3c778f2a", "sha256_in_prefix": "d018b6f489be3f3fd2bcab4a564abbb32dad60476bbdfbc23b27b17d3c778f2a", "size_in_bytes": 43963}, {"_path": "Lib/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "Lib/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "Lib/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "Lib/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "Lib/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "Lib/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "Lib/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "sha256_in_prefix": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "size_in_bytes": 8775}, {"_path": "Lib/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "sha256_in_prefix": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "size_in_bytes": 4330}, {"_path": "Lib/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "d1221f95729fde0d2134f63c6596eac7fcd5e122b5a28e97e80f39dec80a621e", "sha256_in_prefix": "d1221f95729fde0d2134f63c6596eac7fcd5e122b5a28e97e80f39dec80a621e", "size_in_bytes": 32428}, {"_path": "Lib/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "Lib/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "Lib/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "Lib/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "Lib/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "sha256_in_prefix": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "size_in_bytes": 19370}, {"_path": "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "sha256_in_prefix": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "size_in_bytes": 7881}, {"_path": "Lib/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "Lib/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "Lib/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "77d33dc66c4a408258c0d6456b820fa481442936f120069ed63368571bb5d80c", "sha256_in_prefix": "77d33dc66c4a408258c0d6456b820fa481442936f120069ed63368571bb5d80c", "size_in_bytes": 3190}, {"_path": "Lib/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "Lib/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "sha256_in_prefix": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "cb01dbd4fa39908c4afc8d953985d0648c159aa50d16232d7e16679a0a375dbd", "size": 1781626, "subdir": "win-64", "timestamp": 1738046137813, "url": "https://repo.anaconda.com/pkgs/main/win-64/setuptools-75.8.0-py310haa95532_0.conda", "version": "75.8.0"}